import { fileURLToPath, URL } from 'node:url'
import packageJson from './package.json'
import { defineConfig } from "vite"
import { viteSingleFile } from "vite-plugin-singlefile"
import vanilla from 'vite-plugin-vanilla'
import Sitemap from 'vite-plugin-sitemap'


export default defineConfig({
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url)),
        },
        extensions: ['.js', '.ts', '.json', '.scss', '.css']
    },
    plugins: [
        // viteSingleFile(),
        vanilla({
            include: 'src/pages/**/*.html',
            base: 'src/pages',
        }),
        Sitemap({
            hostname: packageJson.domain,
            extensions: ['html'],
            readable: true,
        }),
    ],
    server: {
        host: true
    }
})