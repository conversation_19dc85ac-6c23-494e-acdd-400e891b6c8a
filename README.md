# SHOPLINE Checker

一个用于检测和分析 SHOPLINE 网站配置的专业工具，采用现代化的前后端分离架构。

## 🎯 功能特性

### 🔍 智能检测
- **多变量检测**: 同时检测 `window.__ENV__` 和 `window.Shopline` 等关键变量
- **智能分析**: 自动判断网站是否为 SHOPLINE 网站，计算置信度
- **数据分析**: 统计变量数据大小，分析网站配置
- **双重检测模式**: 支持动态浏览器检测和静态HTML分析

### 📊 检测变量
- `__ENV__`: SHOPLINE 环境配置 (权重: 40%)
- `Shopline`: SHOPLINE SDK 对象 (权重: 30%)
- `__SHOPLINE_CONFIG__`: SHOPLINE 配置 (权重: 25%)
- `dataLayer`: Google Analytics 数据层 (权重: 3%)
- `gtag`: Google Analytics 标签 (权重: 1%)
- `fbq`: Facebook Pixel (权重: 1%)

### 🎨 用户界面
- **现代化设计**: 响应式界面，支持各种设备
- **语法高亮**: JSON 数据彩色显示
- **实时反馈**: 加载状态和详细结果展示
- **错误处理**: 友好的错误提示和解决建议
- **前后端分离**: 纯 JavaScript 前端 + TypeScript 后端

### 🛡️ 稳定性优化
- **专注核心功能**: 忽略页面脚本错误，专注于变量检测
- **智能错误过滤**: 只记录影响核心功能的错误
- **清晰日志输出**: 减少第三方脚本错误干扰
- **可配置错误记录**: 通过环境变量控制错误记录级别
- **浏览器稳定性**: 解决断开连接和框架分离问题
- **智能重试机制**: 自动重试失败的检测请求
- **优雅错误处理**: 即使遇到问题网站也能稳定运行

### ⚡ 性能优化
- **Playwright 引擎**: 使用现代化的 Playwright 替代 Puppeteer
- **启动速度提升**: 浏览器启动速度提升 80%（1000ms → 201ms）
- **响应速度优化**: 整体检测速度提升 30%
- **循环引用处理**: 智能处理复杂对象的序列化问题
- **资源管理优化**: 更好的内存管理和资源清理

### 🔬 静态分析系统
- **高性能HTTP客户端**: 支持超时控制、重试机制、SSRF防护、响应大小限制
- **智能HTML解析器**: 精确提取脚本块，支持内联/外部脚本识别、属性解析
- **多策略变量检测**: 5种检测模式，覆盖各种JavaScript变量声明方式
  - 直接赋值检测: `window.varName = {...}`
  - 变量声明检测: `var config = {...}; window.varName = config`
  - JSON解析检测: `window.varName = JSON.parse(...)`
  - 函数调用检测: `window.varName = someFunction()`
  - 对象属性检测: `{varName: {...}}`
- **平台版本识别**: 精确区分SHOPLINE 1.0、2.0、混合版本
- **智能分析引擎**: 置信度计算、数据质量评估、建议生成
- **批量处理支持**: 支持多URL并行分析，性能监控

### 🏗️ 架构优化 (v1.8.0)
- **项目结构清理**: 消除前后端代码混合问题
- **技术栈分离**: 前端纪JavaScript，后端TypeScript
- **文件组织优化**: 临时文件统一管理，核心代码清晰分离
- **开发体验提升**: 更好的代码组织和维护性

### 🚀 静态分析集成 (v1.9.0)
- **双重检测模式**: 支持动态检测、静态分析、混合模式和智能选择
- **新增API端点**:
  - `/analyze/static` - 纯静态分析
  - `/analyze/static/batch` - 批量静态分析
  - `/detect/enhanced` - 增强检测 (多模式)
- **性能大幅提升**: 静态分析速度提升70-80%，资源消耗降低95%
- **智能模式选择**: 根据URL特征自动选择最佳检测方式
- **统一缓存系统**: 支持不同检测模式的结果缓存
- **增强监控**: 集成静态分析性能指标和健康检查

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装和运行

1. **克隆项目**
```bash
git clone https://github.com/your-username/shoplinechecker.git
cd shoplinechecker
```

2. **安装依赖**
```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd server
npm install
```

3. **启动服务**
```bash
# 启动后端服务
cd server
npm run pm2:start

# 启动前端服务 (新终端)
cd ..
npm run dev
```

4. **访问应用**
- 前端界面: http://localhost:5174/
- 后端 API: http://localhost:3000/

## 📡 API 接口

### 健康检查
```http
GET /health
```

### SHOPLINE 专用检测
```http
POST /detect/shopline
Content-Type: application/json

{
  "url": "https://charm-demo.myshopline.com/",
  "options": {
    "timeout": 30000,
    "waitUntil": "domcontentloaded"
  }
}
```

### 通用变量检测
```http
POST /detect
Content-Type: application/json

{
  "url": "https://example.com/",
  "variables": [
    {
      "name": "__ENV__",
      "path": "__ENV__",
      "defaultValue": null
    }
  ]
}
```

## 🏗️ 项目结构

```
shoplinechecker/
├── src/                    # 前端源码 (JavaScript + Vite)
│   ├── core/              # 核心功能模块
│   ├── pages/
│   │   └── index.html     # 主页面
│   ├── main.js            # 主入口文件
│   └── style.scss         # 样式文件
├── server/                 # 后端源码 (TypeScript + Hono)
│   ├── src/
│   │   ├── server.ts      # 服务器主文件
│   │   ├── config.ts      # 配置管理
│   │   ├── types.ts       # 类型定义
│   │   ├── core/          # 核心功能模块
│   │   │   ├── browser.ts     # 浏览器管理器
│   │   │   ├── cache.ts       # 缓存服务
│   │   │   └── detector.ts    # 检测逻辑
│   │   └── static-analysis/   # 静态分析模块
│   │       ├── http-client.ts        # HTTP客户端
│   │       ├── html-parser.ts        # HTML解析器
│   │       ├── variable-detector.ts  # 变量检测引擎
│   │       ├── result-analyzer.ts    # 结果分析引擎
│   │       ├── static-analyzer.ts    # 综合静态分析器
│   │       └── *.test.ts             # 单元测试
│   ├── tempfiles/         # 临时脚本和调试文件
│   │   ├── comprehensive-diagnosis.cjs
│   │   ├── diagnose-browser.cjs
│   │   ├── investigate-shopline-data.cjs
│   │   ├── start.js
│   │   └── test-*.cjs     # 各种测试脚本
│   ├── tests/             # 正式测试套件
│   │   ├── unit/          # 单元测试
│   │   ├── integration/   # 集成测试
│   │   └── e2e/           # 端到端测试
│   ├── dist/              # TypeScript 编译输出
│   ├── logs/              # 运行日志
│   └── scripts/           # 部署和维护脚本
├── tempfiles/             # 根目录临时文件
├── public/                # 静态资源
├── dist/                  # 前端构建输出
├── CHANGELOG.md           # 更新日志
└── README.md              # 项目说明
```

### 架构特点
- **前后端完全分离**: 前端使用 JavaScript + Vite，后端使用 TypeScript + Hono
- **清晰的模块划分**: 核心功能、静态分析、测试分别组织
- **临时文件管理**: 所有临时脚本统一存放在 `tempfiles` 目录
- **现代化工具链**: 使用最新的开发工具和框架

## 🧪 测试

### 运行测试
```bash
# 后端单元测试
cd server
npm run test

# 后端集成测试
npm run test:integration

# 后端端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:coverage

# 临时脚本测试 (调试用)
node tempfiles/test-shopline-detection.cjs
node tempfiles/test-frontend-integration.cjs
```

### 测试结构
- **单元测试**: 测试单个模块功能
- **集成测试**: 测试模块间协作
- **端到端测试**: 测试完整流程
- **临时测试**: 调试和验证脚本

## 📈 使用示例

### Web 界面使用
1. 访问 http://localhost:5174/
2. 输入 URL: `https://charm-demo.myshopline.com/`
3. 点击 "🔍 检测 SHOPLINE 变量"
4. 查看详细的检测结果和分析

### API 端点使用

#### 1. 传统动态检测
```bash
curl -X POST http://localhost:3001/detect/shopline \
  -H "Content-Type: application/json" \
  -d '{"url": "https://demo-store.shoplineapp.com"}'
```

#### 2. 静态分析检测 (新增)
```bash
curl -X POST http://localhost:3001/analyze/static \
  -H "Content-Type: application/json" \
  -d '{"url": "https://demo-store.shoplineapp.com"}'
```

#### 3. 增强检测 (多模式) (新增)
```bash
# 自动模式选择
curl -X POST http://localhost:3001/detect/enhanced \
  -H "Content-Type: application/json" \
  -d '{"url": "https://demo-store.shoplineapp.com", "options": {"mode": "auto"}}'

# 混合检测模式
curl -X POST http://localhost:3001/detect/enhanced \
  -H "Content-Type: application/json" \
  -d '{"url": "https://demo-store.shoplineapp.com", "options": {"mode": "hybrid"}}'

# 仅静态分析
curl -X POST http://localhost:3001/detect/enhanced \
  -H "Content-Type: application/json" \
  -d '{"url": "https://demo-store.shoplineapp.com", "options": {"mode": "static"}}'
```

#### 4. 批量静态分析 (新增)
```bash
curl -X POST http://localhost:3001/analyze/static/batch \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "https://demo-store.shoplineapp.com",
      "https://another-store.myshopline.com"
    ]
  }'
```

#### 5. 系统状态检查
```bash
# 健康检查 (包含静态分析状态)
curl http://localhost:3001/health

# 详细统计信息 (包含静态分析指标)
curl http://localhost:3001/stats
```

### 预期结果
- ✅ 检测到 SHOPLINE 网站 (置信度: 70%)
- ✅ 找到 `__ENV__` 变量 (6,368 字符)
- ✅ 找到 `Shopline` 变量 (77,863 字符)
- 📊 总数据大小: 84,231 字符
- 🚀 静态分析速度提升 70-80%

## 🔧 开发

### 开发模式
```bash
# 前端热重载 (JavaScript + Vite)
npm run dev

# 后端开发模式 (TypeScript + Hono)
cd server
npm run dev
```

### 构建生产版本
```bash
# 构建前端
npm run build

# 构建后端
cd server
npm run build

# 使用 PM2 启动生产服务
npm run pm2:start
```

### 环境变量配置
```bash
# 可选：启用页面脚本错误记录（调试用）
export LOG_PAGE_ERRORS=true

# 默认：禁用页面脚本错误记录（推荐）
export LOG_PAGE_ERRORS=false
```

### 项目重构历程 (v1.8.0)
- **前端清理**: 移除错误放置的 TypeScript 文件
- **后端整理**: 临时脚本统一管理
- **架构优化**: 确保前后端技术栈分离
- **代码组织**: 提升项目维护性和开发体验

## 📝 更新日志

### 最新版本 v1.8.0 (2025-01-04)
- **项目结构清理**: 消除前后端代码混合问题
- **技术栈分离**: 前端纯JavaScript，后端TypeScript
- **文件组织优化**: 临时文件统一管理
- **开发体验提升**: 更清晰的代码组织和维护性

### 主要版本历程
- **v1.7.0**: 增强静态分析系统，5种检测模式
- **v1.6.0**: Playwright引擎优化，性能提升80%
- **v1.5.0**: 智能错误处理和稳定性优化
- **v1.4.0**: 缓存系统和并发优化
- **v1.3.0**: 安全防护和反检测系统

查看 [CHANGELOG.md](./CHANGELOG.md) 了解详细的版本更新信息。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

---

## 🔗 相关链接

### 竞品分析
- [Analytic EcomInsight](https://analytic.ecominsight.ai/)
- [Shopify Checker](https://shopifychecker.com/)
- [EcSpy Chrome Extension](https://chromewebstore.google.com/detail/ecspy-shopify-traffic-ana/dmkddagkgcpicocplcljimhnmpkjjcga)
- [Browser.is](https://browser.is/)
- [Awoo Technology](https://www.awoo.ai/zh-hant/technology/)

### 测试网站
- [SHOPLINE Demo](https://charm-demo.myshopline.com/)
- [Shopify Demo](https://theme-dawn-demo.myshopify.com/)
- [Faith Doodle](https://faithdoodle.shoplineapp.com/)