/**
 * Vitest 配置文件
 * 配置测试环境和测试选项
 */

import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    // 测试环境
    environment: 'node',
    
    // 全局设置
    globals: true,
    
    // 测试文件匹配模式
    include: [
      'tests/**/*.{test,spec}.{js,ts}',
      'src/**/*.{test,spec}.{js,ts}'
    ],
    
    // 排除的文件
    exclude: [
      'node_modules',
      'dist',
      'coverage',
      '.nyc_output'
    ],
    
    // 测试超时时间
    testTimeout: 30000,
    
    // 钩子超时时间
    hookTimeout: 30000,
    
    // 设置文件
    setupFiles: ['./tests/setup.ts'],
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'coverage/**',
        'dist/**',
        'node_modules/**',
        'tests/**',
        '**/*.d.ts',
        '**/*.config.*',
        '**/types/**'
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        }
      }
    },
    
    // 并发设置
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1
      }
    },
    
    // 报告器
    reporter: ['verbose', 'json'],
    
    // 监视模式
    watch: false,
    
    // 静默模式
    silent: false,
    
    // 隔离模式
    isolate: true,
    
    // 序列化测试
    sequence: {
      shuffle: false
    }
  },
  
  // 路径解析
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  
  // 定义全局变量
  define: {
    'process.env.NODE_ENV': '"test"'
  }
});
