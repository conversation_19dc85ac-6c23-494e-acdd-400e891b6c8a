{"name": "shopline-checker-server", "version": "1.9.0", "description": "ShopLine Checker Backend Service - 基于 Node.js、Hono.js 和 Playwright 的网页变量检测服务，支持静态分析和动态检测双重模式", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "vitest run", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:e2e": "vitest run tests/e2e", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:static-integration": "node tempfiles/test-static-analysis-integration.cjs", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "pm2:start": "pm2 start ecosystem.config.cjs", "pm2:stop": "pm2 stop ecosystem.config.cjs", "pm2:restart": "pm2 restart ecosystem.config.cjs", "pm2:reload": "pm2 reload ecosystem.config.cjs", "pm2:delete": "pm2 delete ecosystem.config.cjs", "pm2:logs": "pm2 logs shopline-checker-server", "pm2:monit": "pm2 monit", "pm2:status": "pm2 status", "deploy:prod": "pm2 deploy ecosystem.config.cjs production", "deploy:staging": "pm2 deploy ecosystem.config.cjs staging"}, "keywords": ["shopline", "checker", "puppeteer", "hono", "nodejs", "variable-detection", "web-scraping"], "author": "ShopLine Checker Team", "license": "MIT", "dependencies": {"@hono/node-server": "^1.12.2", "@types/express-rate-limit": "^5.1.3", "@types/ioredis": "^4.28.10", "dotenv": "^17.2.1", "express-rate-limit": "^8.0.1", "hono": "^4.6.3", "ioredis": "^5.7.0", "jsdom": "^26.1.0", "playwright": "^1.54.2", "zod": "^4.0.14"}, "devDependencies": {"@types/node": "^20.16.10", "pm2": "^6.0.8", "tsx": "^4.19.1", "typescript": "^5.6.2", "vitest": "^3.2.4"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}