#!/bin/bash

# ShopLine Checker Server 部署脚本

set -e

ENVIRONMENT=${1:-production}

echo "🚀 Deploying ShopLine Checker Server to $ENVIRONMENT..."

# 检查环境参数
if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "staging" ]]; then
    echo "❌ Invalid environment. Use 'production' or 'staging'"
    exit 1
fi

# 安装依赖
echo "📦 Installing dependencies..."
npm ci --only=production

# 构建应用
echo "🔨 Building application..."
npm run build

# 运行类型检查
echo "🔍 Running type check..."
npm run type-check

# 运行测试
echo "🧪 Running tests..."
npm test

# 创建必要的目录
echo "📁 Creating directories..."
mkdir -p logs
mkdir -p tempfiles

# 设置文件权限
echo "🔐 Setting permissions..."
chmod +x scripts/*.sh

# 备份当前运行的应用（如果存在）
if pm2 list | grep -q "shopline-checker-server"; then
    echo "💾 Creating backup..."
    pm2 dump
fi

# 重新加载应用
echo "🔄 Reloading application..."
if [[ "$ENVIRONMENT" == "production" ]]; then
    pm2 reload ecosystem.config.cjs --env production
else
    pm2 reload ecosystem.config.cjs --env development
fi

# 保存 PM2 配置
pm2 save

echo "✅ Deployment completed successfully!"
echo "📊 Application status:"
pm2 status
