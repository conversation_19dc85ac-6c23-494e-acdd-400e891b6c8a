#!/bin/bash

# ShopLine Checker Server 启动脚本
# 用于生产环境部署

set -e

echo "🚀 Starting ShopLine Checker Server with PM2..."

# 检查是否安装了 PM2
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 is not installed. Installing PM2..."
    npm install -g pm2
fi

# 检查是否已构建
if [ ! -d "dist" ]; then
    echo "📦 Building application..."
    npm run build
fi

# 创建日志目录
mkdir -p logs

# 停止现有进程（如果存在）
echo "🛑 Stopping existing processes..."
pm2 stop ecosystem.config.cjs 2>/dev/null || true
pm2 delete ecosystem.config.cjs 2>/dev/null || true

# 启动应用
echo "🎯 Starting application..."
pm2 start ecosystem.config.cjs --env production

# 保存 PM2 进程列表
pm2 save

# 设置 PM2 开机自启
pm2 startup

echo "✅ ShopLine Checker Server started successfully!"
echo "📊 Use 'npm run pm2:status' to check status"
echo "📝 Use 'npm run pm2:logs' to view logs"
echo "🔍 Use 'npm run pm2:monit' to monitor processes"
