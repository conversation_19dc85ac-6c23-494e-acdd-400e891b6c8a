/**
 * PM2 配置文件
 * 用于生产环境部署和进程管理
 */

module.exports = {
  apps: [
    {
      // 应用名称
      name: 'shopline-checker-server',

      // 启动脚本
      script: './dist/server.js',

      // 实例数量（cluster 模式）
      instances: '1', // 或者指定数字，如 2

      // 执行模式
      exec_mode: 'cluster',

      // 自动重启
      autorestart: true,

      // 监听文件变化（生产环境建议设为 false）
      watch: false,

      // 最大内存限制（超过后重启）
      max_memory_restart: '1G',

      // 环境变量
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        LOG_LEVEL: 'info',
        NODE_OPTIONS: '--experimental-specifier-resolution=node',
        // 禁用页面脚本错误记录，专注于核心功能
        LOG_PAGE_ERRORS: 'false'
      },

      // 开发环境变量
      env_development: {
        NODE_ENV: 'development',
        PORT: 3001,
        LOG_LEVEL: 'debug',
        // 开发环境也禁用页面脚本错误记录
        LOG_PAGE_ERRORS: 'false'
      },

      // 测试环境变量
      env_test: {
        NODE_ENV: 'test',
        PORT: 3002,
        LOG_LEVEL: 'error'
      },

      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

      // 合并日志
      merge_logs: true,

      // 时间戳
      time: true,

      // 进程 ID 文件
      pid_file: './logs/pm2.pid',

      // 最小正常运行时间
      min_uptime: '10s',

      // 最大重启次数
      max_restarts: 10,

      // 重启延迟
      restart_delay: 4000,

      // 优雅关闭超时时间
      kill_timeout: 5000,

      // 监听重启
      listen_timeout: 3000,

      // 忽略的监听文件
      ignore_watch: [
        'node_modules',
        'logs',
        'coverage',
        'tests'
      ],

      // 实例间负载均衡
      instance_var: 'INSTANCE_ID',

      // 源码映射支持
      source_map_support: true
    }
  ],

  // 部署配置
  deploy: {
    // 生产环境部署
    production: {
      user: 'deploy',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: '**************:your-username/shopline-checker.git',
      path: '/var/www/shopline-checker',
      'post-deploy': 'cd server && npm install && npm run build && pm2 reload ecosystem.config.cjs --env production',
      'pre-setup': 'apt update && apt install git -y'
    },

    // 预发布环境部署
    staging: {
      user: 'deploy',
      host: ['staging-server.com'],
      ref: 'origin/develop',
      repo: '**************:your-username/shopline-checker.git',
      path: '/var/www/shopline-checker-staging',
      'post-deploy': 'cd server && npm install && npm run build && pm2 reload ecosystem.config.cjs --env development',
      'pre-setup': 'apt update && apt install git -y'
    }
  }
};
