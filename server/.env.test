# 测试环境配置
NODE_ENV=test
PORT=3001
HOST=localhost

# 数据库配置
REDIS_URL=redis://localhost:6379/1
MONGODB_URL=mongodb://localhost:27017/shopline-checker-test

# 浏览器配置
BROWSER_POOL_MIN=1
BROWSER_POOL_MAX=2
BROWSER_MAX_PAGES_PER_INSTANCE=2
BROWSER_IDLE_TIMEOUT=30000
BROWSER_HEALTH_CHECK_INTERVAL=10000
BROWSER_TIMEOUT=10000
PAGE_TIMEOUT=5000

# 缓存配置
CACHE_TTL=300
CACHE_MAX_SIZE=100
CACHE_ENABLE_REDIS=false
CACHE_KEY_PREFIX=test:

# 日志配置
LOG_LEVEL=error
LOG_FILE_PATH=./logs/test

# 限流配置
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_SUCCESSFUL=true

# 安全配置
JWT_SECRET=test-secret-key
API_KEY_REQUIRED=false
CORS_ORIGIN=*
BLOCK_PRIVATE_IPS=false

# 并发控制
MAX_CONCURRENT_REQUESTS=10
QUEUE_SIZE=50
QUEUE_TIMEOUT=10000

# 重试配置
RETRY_MAX_ATTEMPTS=1
RETRY_BASE_DELAY=100
RETRY_MAX_DELAY=1000
RETRY_BACKOFF_FACTOR=1
RETRY_JITTER=false

# Puppeteer 配置
PUPPETEER_HEADLESS=true
PUPPETEER_ARGS=--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--disable-gpu

# 资源优化
BLOCK_IMAGES=true
BLOCK_STYLESHEETS=true
BLOCK_FONTS=true
BLOCK_MEDIA=true
BLOCK_ADS=true
