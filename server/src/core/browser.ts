/**
 * 简化的浏览器管理器
 * 专注于核心功能，移除过度复杂的池化逻辑
 */

import { chromium, <PERSON><PERSON>er, Page, BrowserContext } from 'playwright';
import { log } from '../config';

class SimpleBrowserManager {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private activePage: Page | null = null;
  private pageUsageCount = 0;
  private readonly maxPageUsage = 50; // 页面最大使用次数

  /**
   * 初始化浏览器
   */
  async initialize(): Promise<void> {
    if (!this.browser) {
      try {
        this.browser = await chromium.launch({
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu'
          ],
          timeout: 30000
        });

        // 创建浏览器上下文
        this.context = await this.browser.newContext({
          viewport: { width: 1366, height: 768 },
          ignoreHTTPSErrors: true,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        });

        log.info('Browser initialized successfully');

        // 添加错误处理
        this.browser.on('disconnected', () => {
          log.warn('Browser disconnected, will reinitialize on next request');
          this.cleanup();
        });

      } catch (error) {
        log.error('Failed to initialize browser', error);
        throw error;
      }
    }
  }

  /**
   * 获取页面实例
   */
  async getPage(): Promise<Page> {
    await this.initialize();

    // 如果当前页面使用次数过多或不存在，创建新页面
    if (!this.activePage || this.pageUsageCount >= this.maxPageUsage) {
      await this.createNewPage();
    }

    this.pageUsageCount++;
    
    log.info('Page acquired', { 
      usageCount: this.pageUsageCount,
      maxUsage: this.maxPageUsage 
    });

    return this.activePage!;
  }

  /**
   * 创建新页面
   */
  private async createNewPage(): Promise<void> {
    try {
      // 关闭旧页面
      if (this.activePage && !this.activePage.isClosed()) {
        await this.activePage.close();
      }

      // 创建新页面
      if (!this.context) {
        throw new Error('Browser context not available');
      }

      this.activePage = await this.context.newPage();
      this.pageUsageCount = 0;

      // 设置页面配置
      this.activePage.setDefaultTimeout(30000);
      this.activePage.setDefaultNavigationTimeout(30000);

      // 添加页面错误处理
      this.activePage.on('pageerror', (error) => {
        log.warn('Page error occurred', { error: error.message });
      });

      this.activePage.on('requestfailed', (request) => {
        const url = request.url();
        const failure = request.failure()?.errorText;

        // 过滤掉常见的第三方服务和非核心资源请求失败（这些不影响变量检测功能）
        const ignoredPatterns = [
          // Cloudflare 服务
          '/cdn-cgi/rum',           // Cloudflare RUM
          '/cdn-cgi/beacon',        // Cloudflare Beacon

          // Google 服务
          'google-analytics.com',   // Google Analytics
          'analytics.google.com',   // Google Analytics (新域名)
          'googletagmanager.com',   // Google Tag Manager
          'doubleclick.net',        // Google Ads
          'googlesyndication.com',  // Google AdSense
          'google.com/recaptcha',   // Google reCAPTCHA
          'fonts.gstatic.com',      // Google Fonts
          '/g/collect?',            // Google Analytics collect endpoint

          // 社交媒体服务
          'facebook.com',           // Facebook (所有Facebook服务)
          'youtube.com',            // YouTube (所有YouTube服务)
          'instagram.com',          // Instagram
          'graph.instagram.com',    // Instagram Graph API

          // 第三方分析和营销服务
          'hotjar.com',             // Hotjar
          'segment.com',            // Segment
          'mixpanel.com',           // Mixpanel
          'zotabox.com',            // Zotabox
          'events.shoplytics.com',  // SHOPLINE Analytics
          'shoplytics.com',         // SHOPLINE Analytics

          // SHOPLINE 非核心资源（不影响变量检测）
          'shoplineimg.com',        // SHOPLINE 图片资源
          'cdn.shoplineapp.com',    // SHOPLINE CDN 资源
          'static.shoplineapp.com', // SHOPLINE 静态资源
          'r2cdn.myshopline.com',   // SHOPLINE R2 CDN

          // 通用资源类型（通常不影响变量检测）
          '.woff2',                 // 字体文件
          '.woff',                  // 字体文件
          '.ttf',                   // 字体文件
          '.gif',                   // GIF图片
          '.webp',                  // WebP图片
          '.png',                   // PNG图片
          '.jpg',                   // JPG图片
          '.jpeg',                  // JPEG图片

          // SHOPLINE API 调用（业务功能，不影响变量检测）
          '/api/merchants/',        // 商户API
          '/promotions/',           // 促销API
          '/cart/count'             // 购物车API
        ];

        const shouldIgnore = ignoredPatterns.some(pattern => url.includes(pattern));

        if (!shouldIgnore) {
          log.warn('Request failed', {
            url,
            failure
          });
        }
      });

      log.info('New page created successfully');

    } catch (error) {
      log.error('Failed to create new page', error);
      throw error;
    }
  }

  /**
   * 释放页面（简化版，不需要复杂的池化逻辑）
   */
  async releasePage(_page: Page): Promise<void> {
    // 在简化版本中，我们不立即关闭页面，而是复用它
    // 页面会在达到使用次数限制时自动更新
    log.info('Page released for reuse');
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    hasActiveBrowser: boolean;
    hasActivePage: boolean;
    pageUsageCount: number;
    maxPageUsage: number;
  } {
    return {
      hasActiveBrowser: !!this.browser,
      hasActivePage: !!this.activePage && !this.activePage.isClosed(),
      pageUsageCount: this.pageUsageCount,
      maxPageUsage: this.maxPageUsage
    };
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    try {
      if (this.activePage && !this.activePage.isClosed()) {
        await this.activePage.close();
      }

      if (this.context) {
        await this.context.close();
      }

      if (this.browser) {
        await this.browser.close();
      }

      this.activePage = null;
      this.context = null;
      this.browser = null;
      this.pageUsageCount = 0;

      log.info('Browser cleanup completed');

    } catch (error) {
      log.error('Error during browser cleanup', error);
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.browser || !this.browser.isConnected()) {
        return false;
      }

      if (!this.activePage || this.activePage.isClosed()) {
        return false;
      }

      // 尝试执行简单操作验证页面是否响应
      await this.activePage.evaluate(() => 'health-check');
      return true;

    } catch (error) {
      log.warn('Browser health check failed', error);
      return false;
    }
  }

  /**
   * 强制重启浏览器
   */
  async restart(): Promise<void> {
    log.info('Restarting browser...');
    await this.cleanup();
    await this.initialize();
    log.info('Browser restarted successfully');
  }
}

// 创建全局实例
export const browserManager = new SimpleBrowserManager();

// 优雅关闭处理
process.on('SIGINT', async () => {
  log.info('Received SIGINT, cleaning up browser...');
  await browserManager.cleanup();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  log.info('Received SIGTERM, cleaning up browser...');
  await browserManager.cleanup();
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', async (error) => {
  log.error('Uncaught exception, cleaning up browser...', error);
  await browserManager.cleanup();
  process.exit(1);
});

process.on('unhandledRejection', async (reason) => {
  log.error('Unhandled rejection, cleaning up browser...', reason);
  await browserManager.cleanup();
  process.exit(1);
});
