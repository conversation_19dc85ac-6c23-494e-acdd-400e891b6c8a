/**
 * 核心检测逻辑
 * 合并所有检测相关功能，提供简洁的网站变量检测
 */

import { Page } from 'playwright';
import { VariableConfig, VariableResult, DetectionOptions } from '../types';
import { log } from '../config';



/**
 * 检测页面变量的主要函数
 * @param page Playwright 页面实例
 * @param url 目标URL
 * @param variables 要检测的变量列表
 * @param options 检测选项
 */
export async function detectPageVariables(
  page: Page,
  url: string,
  variables: VariableConfig[],
  options: DetectionOptions = {}
): Promise<VariableResult[]> {
  try {
    log.info(`Starting detection for ${url}`);
    
    // 设置页面超时
    const timeout = options.timeout || 30000;
    page.setDefaultTimeout(timeout);
    page.setDefaultNavigationTimeout(timeout);

    // 导航到目标页面
    await page.goto(url, { 
      waitUntil: 'domcontentloaded',
      timeout 
    });

    // 等待页面稳定
    await waitForPageStable(page, options.waitTime || 2000);

    // 检测所有变量
    const results: VariableResult[] = [];
    
    for (const variable of variables) {
      try {
        const result = await detectSingleVariable(page, variable);
        results.push(result);
        
        log.info(`Variable detection completed`, {
          name: variable.name,
          found: result.found,
          type: result.type
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        results.push({
          name: variable.name,
          found: false,
          value: null,
          type: 'undefined',
          size: 0,
          error: errorMessage
        });
        
        log.warn(`Variable detection failed`, {
          name: variable.name,
          error: errorMessage
        });
      }
    }

    return results;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    log.error(`Page detection failed for ${url}`, { error: errorMessage });
    
    // 返回所有变量的失败结果
    return variables.map(variable => ({
      name: variable.name,
      found: false,
      value: null,
      type: 'undefined',
      size: 0,
      error: errorMessage
    }));
  }
}

/**
 * 检测单个变量
 */
async function detectSingleVariable(page: Page, variable: VariableConfig): Promise<VariableResult> {
  try {
    // 在页面上下文中执行检测脚本
    const result = await page.evaluate((varConfig) => {
      try {
        // 解析变量路径
        const pathParts = varConfig.path.split('.');
        let current: any = (globalThis as any).window || globalThis;
        
        // 遍历路径
        for (const part of pathParts) {
          if (part === 'window') continue; // 跳过 window 前缀
          
          if (current && typeof current === 'object' && part in current) {
            current = current[part];
          } else {
            return {
              found: false,
              value: null,
              type: 'undefined',
              size: 0
            };
          }
        }

        // 变量存在，分析其内容
        const type = typeof current;
        let value = current;
        let size = 0;

        // 计算大小和处理特殊类型
        if (current === null) {
          size = 0;
          value = null;
        } else if (type === 'string') {
          size = current.length;
        } else if (type === 'object') {
          try {
            // 在浏览器上下文中处理循环引用
            const seen = new WeakSet();

            function serializeBrowserObject(obj: any, maxDepth: number = 10): any {
              function replacer(_key: string, value: any, depth: number = 0): any {
                if (depth > maxDepth) {
                  return '[Max Depth Reached]';
                }

                if (value === null || value === undefined) {
                  return value;
                }

                if (typeof value !== 'object') {
                  return value;
                }

                if (seen.has(value)) {
                  return '[Circular Reference]';
                }

                if (Array.isArray(value)) {
                  seen.add(value);
                  const result = value.map((item, index) => {
                    try {
                      return replacer(index.toString(), item, depth + 1);
                    } catch {
                      return '[Serialization Error]';
                    }
                  });
                  seen.delete(value);
                  return result;
                }

                if (typeof value === 'object') {
                  seen.add(value);
                  const result: any = {};

                  try {
                    for (const [k, v] of Object.entries(value)) {
                      try {
                        result[k] = replacer(k, v, depth + 1);
                      } catch {
                        result[k] = '[Property Serialization Error]';
                      }
                    }
                  } catch {
                    seen.delete(value);
                    return '[Object Enumeration Error]';
                  }

                  seen.delete(value);
                  return result;
                }

                return value;
              }

              return replacer('', obj);
            }

            // 序列化对象
            value = serializeBrowserObject(current);

            // 计算大小
            try {
              const jsonString = JSON.stringify(value, null, 2);
              size = jsonString.length;
            } catch {
              // 如果仍然无法序列化，使用估算大小
              size = JSON.stringify(current.toString()).length;
            }

          } catch (error) {
            // 最后的兜底处理
            value = `[Serialization Error: ${error instanceof Error ? error.message : 'Unknown error'}]`;
            size = 0;
          }
        } else if (type === 'function') {
          value = '[Function]';
          size = current.toString().length;
        } else {
          size = JSON.stringify(current).length;
        }

        return {
          found: true,
          value,
          type,
          size
        };

      } catch (error) {
        return {
          found: false,
          value: null,
          type: 'undefined',
          size: 0,
          error: error instanceof Error ? error.message : 'Evaluation error'
        };
      }
    }, variable);

    return {
      name: variable.name,
      ...result
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      name: variable.name,
      found: false,
      value: null,
      type: 'undefined',
      size: 0,
      error: errorMessage
    };
  }
}

/**
 * 等待页面稳定
 */
async function waitForPageStable(page: Page, waitTime: number): Promise<void> {
  try {
    // 等待网络空闲
    await page.waitForLoadState('networkidle', { timeout: 5000 });
  } catch {
    // 如果网络空闲等待失败，使用固定等待时间
    await page.waitForTimeout(waitTime);
  }
}

/**
 * 分析检测结果
 */
export function analyzeResults(results: VariableResult[]): {
  foundCount: number;
  totalCount: number;
  foundVariables: string[];
  missingVariables: string[];
  hasShopline: boolean;
  summary: string;
  // 前端期望的字段
  isShoplineStore: boolean;
  storeType: string;
  storeVersion?: string;
  confidence: number;
  totalVariablesFound: number;
  totalDataSize: number;
} {
  const foundVariables = results.filter(r => r.found).map(r => r.name);
  const missingVariables = results.filter(r => !r.found).map(r => r.name);

  // 检测 SHOPLINE 平台版本
  const hasShopline2 = foundVariables.includes('Shopline') && foundVariables.includes('__ENV__');
  const hasShopline1 = foundVariables.includes('mainConfig') && foundVariables.includes('shopline');
  const hasShopline = hasShopline1 || hasShopline2;

  // 计算置信度
  let confidence = 0;
  let platformVersion = '';

  if (hasShopline2) {
    // SHOPLINE 2.0 平台
    confidence = 80; // 基础分数
    platformVersion = '2.0';

    // 找到其他 Shopline 相关变量，每个 +5%
    const otherShoplineVars = foundVariables.filter(name =>
      name.toLowerCase().includes('shopline') && !['Shopline', '__ENV__'].includes(name)
    );
    confidence += otherShoplineVars.length * 5;

  } else if (hasShopline1) {
    // SHOPLINE 1.0 平台
    confidence = 75; // 基础分数
    platformVersion = '1.0';

    // 检查 mainConfig 的大小，大的配置对象增加置信度
    const mainConfigResult = results.find(r => r.name === 'mainConfig' && r.found);
    if (mainConfigResult && mainConfigResult.size > 10000) {
      confidence += 15; // 大型配置对象增加置信度
    }

  } else {
    // 检查是否有任何 SHOPLINE 相关变量
    const anyShoplineVar = foundVariables.some(name =>
      name.toLowerCase().includes('shopline')
    );
    if (anyShoplineVar) {
      confidence = 40; // 低置信度
      platformVersion = 'Unknown';
    }
  }

  // 最高100%
  confidence = Math.min(confidence, 100);

  // 计算总数据大小
  const totalDataSize = results.reduce((sum, r) => sum + (r.size || 0), 0);

  // 确定商店类型和版本
  let storeType = 'Unknown';
  let storeVersion: string | undefined;

  if (hasShopline2) {
    storeType = 'SHOPLINE 2.0 Store';

    // 尝试从 __ENV__ 中获取环境信息
    const envResult = results.find(r => r.name === '__ENV__' && r.found);
    if (envResult && envResult.value && typeof envResult.value === 'object') {
      const env = envResult.value as any;
      if (env.APP_ENV) {
        storeVersion = `Platform: 2.0, Environment: ${env.APP_ENV}`;
      } else {
        storeVersion = 'Platform: 2.0';
      }
    } else {
      storeVersion = 'Platform: 2.0';
    }

  } else if (hasShopline1) {
    storeType = 'SHOPLINE 1.0 Store';

    // 尝试从 mainConfig 中获取信息
    const mainConfigResult = results.find(r => r.name === 'mainConfig' && r.found);
    if (mainConfigResult && mainConfigResult.value && typeof mainConfigResult.value === 'object') {
      const config = mainConfigResult.value as any;
      if (config.merchantId) {
        storeVersion = `Platform: 1.0, Merchant: ${config.merchantId}`;
      } else {
        storeVersion = 'Platform: 1.0';
      }
    } else {
      storeVersion = 'Platform: 1.0';
    }

  } else if (hasShopline) {
    storeType = 'SHOPLINE Store';
    storeVersion = `Platform: ${platformVersion}`;
  }

  let summary = '';
  if (foundVariables.length === 0) {
    summary = 'No target variables found';
  } else if (hasShopline) {
    summary = 'Shopline detected';
  } else {
    summary = `Found ${foundVariables.length} variables`;
  }

  const result: any = {
    foundCount: foundVariables.length,
    totalCount: results.length,
    foundVariables,
    missingVariables,
    hasShopline,
    summary,
    // 前端期望的字段
    isShoplineStore: hasShopline,
    storeType,
    confidence,
    totalVariablesFound: foundVariables.length,
    totalDataSize
  };

  // 只有当 storeVersion 存在时才添加
  if (storeVersion) {
    result.storeVersion = storeVersion;
  }

  return result;
}
