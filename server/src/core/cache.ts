/**
 * 简化的缓存服务
 * 提供基础的内存缓存功能，可选 Redis 支持
 */

import { createHash } from 'crypto';
import { log } from '../config';
import { VariableResult, UnifiedDetectionResult } from '../types';

/**
 * 缓存项接口
 */
interface CacheItem {
  data: VariableResult[] | UnifiedDetectionResult;
  timestamp: number;
  ttl: number;
  type: 'variable-results' | 'unified-detection';
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  size: number;
  enabled: boolean;
}

/**
 * 简化的缓存管理器
 */
class SimpleCacheManager {
  private cache = new Map<string, CacheItem>();
  private stats = { hits: 0, misses: 0 };
  private readonly defaultTTL = 5 * 60 * 1000; // 5分钟
  private readonly maxSize = 1000; // 最大缓存项数
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    // 启动定期清理
    this.startCleanup();
    log.info('Simple cache manager initialized');
  }

  /**
   * 生成缓存键
   */
  private generateKey(url: string, variables: string[], type: string = 'dynamic'): string {
    const content = `${type}:${url}:${variables.sort().join(',')}`;
    return createHash('md5').update(content).digest('hex');
  }

  /**
   * 生成统一检测结果的缓存键
   */
  private generateUnifiedKey(url: string, method: string, options?: any): string {
    const optionsStr = options ? JSON.stringify(options) : '';
    const content = `unified:${method}:${url}:${optionsStr}`;
    return createHash('md5').update(content).digest('hex');
  }

  /**
   * 获取缓存
   */
  async get(url: string, variables: string[]): Promise<VariableResult[] | null> {
    try {
      const key = this.generateKey(url, variables, 'dynamic');
      const item = this.cache.get(key);

      if (!item || item.type !== 'variable-results') {
        this.stats.misses++;
        return null;
      }

      // 检查是否过期
      if (Date.now() - item.timestamp > item.ttl) {
        this.cache.delete(key);
        this.stats.misses++;
        return null;
      }

      this.stats.hits++;
      log.info('Cache hit', { url, key: key.substring(0, 8) });
      return item.data as VariableResult[];

    } catch (error) {
      log.error('Cache get error', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * 设置缓存
   */
  async set(
    url: string, 
    variables: string[], 
    data: VariableResult[], 
    ttl?: number
  ): Promise<void> {
    try {
      const key = this.generateKey(url, variables, 'dynamic');
      const item: CacheItem = {
        data,
        timestamp: Date.now(),
        ttl: ttl || this.defaultTTL,
        type: 'variable-results'
      };

      // 检查缓存大小限制
      if (this.cache.size >= this.maxSize) {
        this.evictOldest();
      }

      this.cache.set(key, item);
      log.info('Cache set', { 
        url, 
        key: key.substring(0, 8),
        ttl: item.ttl,
        size: this.cache.size
      });

    } catch (error) {
      log.error('Cache set error', error);
    }
  }

  /**
   * 设置统一检测结果缓存
   */
  async setUnified(url: string, method: string, data: UnifiedDetectionResult, options?: any, ttl?: number): Promise<void> {
    try {
      const key = this.generateUnifiedKey(url, method, options);
      const item: CacheItem = {
        data,
        timestamp: Date.now(),
        ttl: ttl || this.defaultTTL,
        type: 'unified-detection'
      };

      // 检查缓存大小限制
      if (this.cache.size >= this.maxSize) {
        this.evictOldest();
      }

      this.cache.set(key, item);
      log.info('Unified cache set', {
        url,
        method,
        key: key.substring(0, 8),
        ttl: item.ttl,
        size: this.cache.size
      });

    } catch (error) {
      log.error('Unified cache set error', error);
    }
  }

  /**
   * 获取统一检测结果缓存
   */
  async getUnified(url: string, method: string, options?: any): Promise<UnifiedDetectionResult | null> {
    try {
      const key = this.generateUnifiedKey(url, method, options);
      const item = this.cache.get(key);

      if (!item || item.type !== 'unified-detection') {
        this.stats.misses++;
        return null;
      }

      // 检查是否过期
      if (Date.now() - item.timestamp > item.ttl) {
        this.cache.delete(key);
        this.stats.misses++;
        return null;
      }

      this.stats.hits++;
      log.info('Unified cache hit', { url, method, key: key.substring(0, 8) });
      return item.data as UnifiedDetectionResult;

    } catch (error) {
      log.error('Unified cache get error', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * 清除缓存
   */
  async clear(): Promise<void> {
    try {
      const size = this.cache.size;
      this.cache.clear();
      this.stats = { hits: 0, misses: 0 };
      
      log.info('Cache cleared', { previousSize: size });
    } catch (error) {
      log.error('Cache clear error', error);
    }
  }

  /**
   * 删除特定缓存
   */
  async delete(url: string, variables: string[]): Promise<void> {
    try {
      const key = this.generateKey(url, variables);
      const deleted = this.cache.delete(key);
      
      if (deleted) {
        log.info('Cache item deleted', { url, key: key.substring(0, 8) });
      }
    } catch (error) {
      log.error('Cache delete error', error);
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    const hitRate = total > 0 ? this.stats.hits / total : 0;

    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      hitRate: Math.round(hitRate * 100) / 100,
      size: this.cache.size,
      enabled: true
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = { hits: 0, misses: 0 };
    log.info('Cache stats reset');
  }

  /**
   * 淘汰最旧的缓存项
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      log.info('Evicted oldest cache item', { key: oldestKey.substring(0, 8) });
    }
  }

  /**
   * 启动定期清理
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpired();
    }, 60000); // 每分钟清理一次
  }

  /**
   * 清理过期项
   */
  private cleanupExpired(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      log.info('Cleaned up expired cache items', { 
        count: cleanedCount,
        remaining: this.cache.size 
      });
    }
  }

  /**
   * 停止清理定时器
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.cache.clear();
    log.info('Cache manager destroyed');
  }
}

// 创建全局实例
export const cacheManager = new SimpleCacheManager();

// 优雅关闭处理
process.on('SIGINT', () => {
  cacheManager.destroy();
});

process.on('SIGTERM', () => {
  cacheManager.destroy();
});
