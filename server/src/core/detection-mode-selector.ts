/**
 * 检测模式选择器
 * 根据URL特征和配置智能选择最佳检测模式
 */

import { DetectionMethod, EnhancedDetectionOptions } from '../types';
import { log } from '../config';

export interface ModeSelectionCriteria {
  url: string;
  options: EnhancedDetectionOptions;
  context?: {
    isKnownShoplineStore?: boolean;
    previousResults?: any;
    performanceRequirement?: 'fast' | 'accurate' | 'comprehensive';
  };
}

export interface ModeSelectionResult {
  selectedMode: DetectionMethod;
  reason: string;
  confidence: number;
  fallbackMode?: DetectionMethod;
}

export class DetectionModeSelector {
  
  /**
   * 选择最佳检测模式
   */
  selectMode(criteria: ModeSelectionCriteria): ModeSelectionResult {
    const { url, options, context } = criteria;
    
    // 如果明确指定了模式，直接使用
    if (options.mode && options.mode !== 'auto') {
      const mode = options.mode === 'static' ? 'static-analysis' : options.mode;
      return {
        selectedMode: mode,
        reason: `Explicitly specified mode: ${options.mode}`,
        confidence: 1.0
      };
    }

    // 智能模式选择
    return this.intelligentModeSelection(url, options, context);
  }

  /**
   * 智能模式选择算法
   */
  private intelligentModeSelection(
    url: string, 
    options: EnhancedDetectionOptions,
    context?: ModeSelectionCriteria['context']
  ): ModeSelectionResult {
    
    const factors = this.analyzeSelectionFactors(url, options, context);
    
    // 基于因素评分选择模式
    if (factors.needsHighAccuracy && factors.hasComplexInteractions) {
      return {
        selectedMode: 'hybrid',
        reason: 'High accuracy required with complex interactions detected',
        confidence: 0.9,
        fallbackMode: 'dynamic'
      };
    }
    
    if (factors.needsSpeed && !factors.hasComplexInteractions) {
      return {
        selectedMode: 'static-analysis',
        reason: 'Speed prioritized and no complex interactions detected',
        confidence: 0.85,
        fallbackMode: 'dynamic'
      };
    }
    
    if (factors.isKnownShoplinePattern) {
      return {
        selectedMode: 'static-analysis',
        reason: 'Known SHOPLINE URL pattern detected',
        confidence: 0.8,
        fallbackMode: 'dynamic'
      };
    }
    
    if (factors.hasJavaScriptHeavyFeatures) {
      return {
        selectedMode: 'dynamic',
        reason: 'JavaScript-heavy features detected',
        confidence: 0.85,
        fallbackMode: 'static-analysis'
      };
    }
    
    // 默认使用混合模式以获得最佳结果
    return {
      selectedMode: 'hybrid',
      reason: 'Default comprehensive analysis for unknown patterns',
      confidence: 0.7,
      fallbackMode: 'dynamic'
    };
  }

  /**
   * 分析选择因素
   */
  private analyzeSelectionFactors(
    url: string,
    options: EnhancedDetectionOptions,
    context?: ModeSelectionCriteria['context']
  ) {
    const urlObj = new URL(url);
    
    return {
      // 性能要求
      needsSpeed: context?.performanceRequirement === 'fast' || options.staticOptions?.enablePerformanceMetrics,
      needsHighAccuracy: context?.performanceRequirement === 'accurate' || options.enableComparison,
      
      // URL 模式分析
      isKnownShoplinePattern: this.isKnownShoplinePattern(urlObj),
      
      // 技术特征分析
      hasComplexInteractions: this.hasComplexInteractions(urlObj),
      hasJavaScriptHeavyFeatures: this.hasJavaScriptHeavyFeatures(urlObj),
      
      // 上下文信息
      isKnownShoplineStore: context?.isKnownShoplineStore || false,
      hasPreviousResults: !!context?.previousResults,
      
      // 置信度阈值
      confidenceThreshold: options.confidenceThreshold || 0.8
    };
  }

  /**
   * 检查是否为已知的 SHOPLINE 模式
   */
  private isKnownShoplinePattern(url: URL): boolean {
    const hostname = url.hostname.toLowerCase();
    
    // SHOPLINE 官方域名模式
    const shoplinePatterns = [
      /\.shoplineapp\.com$/,
      /\.myshopline\.com$/,
      /\.shopline\.hk$/,
      /\.shopline\.tw$/,
      /\.shopline\.sg$/,
      /\.shopline\.my$/,
      /\.shopline\.th$/,
      /\.shopline\.vn$/
    ];
    
    return shoplinePatterns.some(pattern => pattern.test(hostname));
  }

  /**
   * 检查是否有复杂的交互特征
   */
  private hasComplexInteractions(url: URL): boolean {
    const pathname = url.pathname.toLowerCase();
    const searchParams = url.searchParams;
    
    // 复杂交互的指标
    const complexFeatures = [
      pathname.includes('/checkout'),
      pathname.includes('/cart'),
      pathname.includes('/account'),
      pathname.includes('/admin'),
      searchParams.has('variant'),
      searchParams.has('preview'),
      searchParams.has('theme'),
      url.hash.length > 0
    ];
    
    return complexFeatures.filter(Boolean).length >= 2;
  }

  /**
   * 检查是否有 JavaScript 重度特征
   */
  private hasJavaScriptHeavyFeatures(url: URL): boolean {
    const pathname = url.pathname.toLowerCase();
    
    // JavaScript 重度特征
    const jsHeavyFeatures = [
      pathname.includes('/spa'),
      pathname.includes('/app'),
      pathname.includes('/dashboard'),
      pathname.includes('/editor'),
      pathname.includes('/builder')
    ];
    
    return jsHeavyFeatures.some(Boolean);
  }

  /**
   * 获取模式选择统计
   */
  getSelectionStats() {
    // 这里可以添加统计逻辑
    return {
      totalSelections: 0,
      modeDistribution: {
        'static-analysis': 0,
        'dynamic': 0,
        'hybrid': 0
      },
      averageConfidence: 0
    };
  }
}

/**
 * 默认的检测模式选择器实例
 */
export const detectionModeSelector = new DetectionModeSelector();

/**
 * 便捷函数：选择检测模式
 */
export function selectDetectionMode(
  url: string,
  options: EnhancedDetectionOptions = {},
  context?: ModeSelectionCriteria['context']
): ModeSelectionResult {
  const criteria: ModeSelectionCriteria = { url, options };
  if (context) {
    criteria.context = context;
  }

  const result = detectionModeSelector.selectMode(criteria);

  log.info('Detection mode selected', {
    url,
    selectedMode: result.selectedMode,
    reason: result.reason,
    confidence: result.confidence
  });

  return result;
}
