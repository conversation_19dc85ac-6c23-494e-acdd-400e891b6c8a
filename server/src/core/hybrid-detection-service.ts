/**
 * 混合检测服务
 * 同时运行静态分析和动态检测，对比和合并结果
 */

import { StaticAnalysisService } from './static-analysis-service';
import { detectPageVariables, analyzeResults } from './detector';
import { browserManager } from './browser';
import { selectDetectionMode } from './detection-mode-selector';
import {
  UnifiedDetectionResult,
  EnhancedDetectRequest,
  EnhancedAnalysis,
  VariableResult,
  PerformanceMetrics,
  DEFAULT_SHOPLINE_VARIABLES
} from '../types';
import { log } from '../config';

export interface HybridDetectionResult extends UnifiedDetectionResult {
  method: 'hybrid';
  comparison: {
    staticResult: UnifiedDetectionResult;
    dynamicResult: UnifiedDetectionResult;
    agreement: {
      variableAgreement: number;
      confidenceAgreement: number;
      overallAgreement: number;
    };
    discrepancies: string[];
    recommendedResult: 'static' | 'dynamic' | 'merged';
  };
}

export class HybridDetectionService {
  constructor() {
    // 不需要预创建静态服务，按需创建
  }

  /**
   * 执行增强检测（支持所有模式）
   */
  async performEnhancedDetection(request: EnhancedDetectRequest): Promise<UnifiedDetectionResult | HybridDetectionResult> {
    const { url, variables = DEFAULT_SHOPLINE_VARIABLES, options = {} } = request;
    
    // 选择检测模式
    const modeSelection = selectDetectionMode(url, options);
    const selectedMode = modeSelection.selectedMode;

    log.info('Starting enhanced detection', {
      url,
      selectedMode,
      reason: modeSelection.reason
    });

    try {
      switch (selectedMode) {
        case 'static-analysis':
          return await this.performStaticDetection(url, options);
        
        case 'dynamic':
          return await this.performDynamicDetection(url, variables, options);
        
        case 'hybrid':
          return await this.performHybridDetection(url, variables, options);
        
        default:
          throw new Error(`Unsupported detection mode: ${selectedMode}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Enhanced detection failed', { url, selectedMode, error: errorMessage });
      
      // 尝试回退模式
      if (modeSelection.fallbackMode && modeSelection.fallbackMode !== selectedMode) {
        log.info('Attempting fallback detection mode', { 
          url, 
          fallbackMode: modeSelection.fallbackMode 
        });
        
        try {
          if (modeSelection.fallbackMode === 'dynamic') {
            return await this.performDynamicDetection(url, variables, options);
          } else if (modeSelection.fallbackMode === 'static-analysis') {
            return await this.performStaticDetection(url, options);
          }
        } catch (fallbackError) {
          log.error('Fallback detection also failed', { 
            url, 
            fallbackMode: modeSelection.fallbackMode,
            error: fallbackError instanceof Error ? fallbackError.message : 'Unknown error'
          });
        }
      }
      
      // 返回错误结果
      return {
        success: false,
        method: selectedMode,
        url,
        timing: { total: 0, detection: 0, analysis: 0 },
        results: [],
        analysis: this.createEmptyAnalysis(),
        timestamp: new Date().toISOString(),
        error: errorMessage
      };
    }
  }

  /**
   * 执行静态检测
   */
  private async performStaticDetection(url: string, options: any): Promise<UnifiedDetectionResult> {
    const service = new StaticAnalysisService(options.staticOptions);
    return await service.analyze(url);
  }

  /**
   * 执行动态检测
   */
  private async performDynamicDetection(
    url: string, 
    variables: any[], 
    options: any
  ): Promise<UnifiedDetectionResult> {
    const page = await browserManager.getPage();
    
    try {
      const startTime = Date.now();
      const results = await detectPageVariables(page, url, variables, options);
      const analysis = analyzeResults(results);
      const totalTime = Date.now() - startTime;

      return {
        success: true,
        method: 'dynamic',
        url,
        timing: {
          total: totalTime,
          detection: totalTime * 0.8, // 估算
          analysis: totalTime * 0.2
        },
        results,
        analysis: this.convertToEnhancedAnalysis(analysis),
        timestamp: new Date().toISOString()
      };
    } finally {
      await browserManager.releasePage(page);
    }
  }

  /**
   * 执行混合检测
   */
  private async performHybridDetection(
    url: string, 
    variables: any[], 
    options: any
  ): Promise<HybridDetectionResult> {
    const startTime = Date.now();
    
    log.info('Starting hybrid detection', { url });
    
    // 并行执行两种检测
    const [staticResult, dynamicResult] = await Promise.allSettled([
      this.performStaticDetection(url, options),
      this.performDynamicDetection(url, variables, options)
    ]);

    const totalTime = Date.now() - startTime;

    // 处理结果
    const staticData = staticResult.status === 'fulfilled' ? staticResult.value : this.createErrorResult(url, 'static-analysis', 'Static analysis failed');
    const dynamicData = dynamicResult.status === 'fulfilled' ? dynamicResult.value : this.createErrorResult(url, 'dynamic', 'Dynamic detection failed');

    // 对比结果
    const comparison = this.compareResults(staticData, dynamicData);
    
    // 合并结果
    const mergedResult = this.mergeResults(staticData, dynamicData);

    const hybridResult: HybridDetectionResult = {
      ...mergedResult,
      method: 'hybrid',
      timing: {
        total: totalTime,
        detection: Math.max(staticData.timing.detection, dynamicData.timing.detection),
        analysis: staticData.timing.analysis + dynamicData.timing.analysis,
        breakdown: {
          ...staticData.timing.breakdown,
          ...(dynamicData.timing.breakdown?.pageLoad && { pageLoad: dynamicData.timing.breakdown.pageLoad }),
          ...(dynamicData.timing.breakdown?.scriptExecution && { scriptExecution: dynamicData.timing.breakdown.scriptExecution })
        }
      },
      comparison
    };

    log.info('Hybrid detection completed', {
      url,
      staticSuccess: staticData.success,
      dynamicSuccess: dynamicData.success,
      overallAgreement: comparison.agreement.overallAgreement,
      recommendedResult: comparison.recommendedResult
    });

    return hybridResult;
  }

  /**
   * 对比两种检测结果
   */
  private compareResults(
    staticResult: UnifiedDetectionResult,
    dynamicResult: UnifiedDetectionResult
  ) {
    const staticVars = new Set(staticResult.analysis.foundVariables);
    const dynamicVars = new Set(dynamicResult.analysis.foundVariables);

    // 变量一致性计算
    const intersection = new Set([...staticVars].filter(x => dynamicVars.has(x)));
    const union = new Set([...staticVars, ...dynamicVars]);
    const variableAgreement = union.size > 0 ? intersection.size / union.size : 1;

    // 置信度一致性计算
    const staticConfidence = staticResult.analysis.confidence || 0;
    const dynamicConfidence = dynamicResult.analysis.confidence || 0;
    const confidenceAgreement = 1 - Math.abs(staticConfidence - dynamicConfidence);

    // 总体一致性计算（加权平均）
    const overallAgreement = (variableAgreement * 0.7 + confidenceAgreement * 0.3);

    log.info('Result comparison metrics', {
      staticVars: Array.from(staticVars),
      dynamicVars: Array.from(dynamicVars),
      intersection: Array.from(intersection),
      variableAgreement,
      confidenceAgreement,
      overallAgreement
    });

    // 识别差异
    const discrepancies: string[] = [];
    
    if (staticResult.analysis.hasShopline !== dynamicResult.analysis.hasShopline) {
      discrepancies.push('SHOPLINE store detection disagreement');
    }
    
    const onlyInStatic = [...staticVars].filter(x => !dynamicVars.has(x));
    const onlyInDynamic = [...dynamicVars].filter(x => !staticVars.has(x));
    
    if (onlyInStatic.length > 0) {
      discrepancies.push(`Variables only found by static analysis: ${onlyInStatic.join(', ')}`);
    }
    
    if (onlyInDynamic.length > 0) {
      discrepancies.push(`Variables only found by dynamic detection: ${onlyInDynamic.join(', ')}`);
    }

    // 推荐结果
    let recommendedResult: 'static' | 'dynamic' | 'merged';
    
    if (overallAgreement > 0.8) {
      recommendedResult = 'merged';
    } else if (staticResult.success && !dynamicResult.success) {
      recommendedResult = 'static';
    } else if (!staticResult.success && dynamicResult.success) {
      recommendedResult = 'dynamic';
    } else if (staticResult.analysis.confidence! > dynamicResult.analysis.confidence!) {
      recommendedResult = 'static';
    } else {
      recommendedResult = 'dynamic';
    }

    return {
      staticResult,
      dynamicResult,
      agreement: {
        variableAgreement,
        confidenceAgreement,
        overallAgreement
      },
      discrepancies,
      recommendedResult
    };
  }

  /**
   * 合并两种检测结果
   */
  private mergeResults(
    staticResult: UnifiedDetectionResult,
    dynamicResult: UnifiedDetectionResult
  ): UnifiedDetectionResult {
    // 合并变量结果
    const mergedResults = this.mergeVariableResults(staticResult.results, dynamicResult.results);
    
    // 合并分析结果
    const mergedAnalysis = this.mergeAnalysis(staticResult.analysis, dynamicResult.analysis);
    
    // 合并性能指标
    const mergedMetrics = this.mergePerformanceMetrics(
      staticResult.performanceMetrics,
      dynamicResult.performanceMetrics
    );

    const result: UnifiedDetectionResult = {
      success: staticResult.success || dynamicResult.success,
      method: 'hybrid',
      url: staticResult.url,
      timing: {
        total: 0, // 将在调用方设置
        detection: 0,
        analysis: 0
      },
      results: mergedResults,
      analysis: mergedAnalysis,
      timestamp: new Date().toISOString()
    };

    if (mergedMetrics) {
      result.performanceMetrics = mergedMetrics;
    }

    return result;
  }

  /**
   * 合并变量结果
   */
  private mergeVariableResults(
    staticResults: VariableResult[], 
    dynamicResults: VariableResult[]
  ): VariableResult[] {
    const merged = new Map<string, VariableResult>();
    
    // 添加静态结果
    staticResults.forEach(result => {
      merged.set(result.name, result);
    });
    
    // 合并动态结果
    dynamicResults.forEach(result => {
      const existing = merged.get(result.name);
      if (existing) {
        // 如果两者都找到了变量，优先使用动态检测的值
        if (existing.found && result.found) {
          merged.set(result.name, {
            ...result,
            size: Math.max(existing.size, result.size)
          });
        } else if (result.found) {
          merged.set(result.name, result);
        }
      } else {
        merged.set(result.name, result);
      }
    });
    
    return Array.from(merged.values());
  }

  /**
   * 合并分析结果
   */
  private mergeAnalysis(
    staticAnalysis: EnhancedAnalysis, 
    dynamicAnalysis: EnhancedAnalysis
  ): EnhancedAnalysis {
    const allFoundVariables = Array.from(new Set([
      ...staticAnalysis.foundVariables,
      ...dynamicAnalysis.foundVariables
    ]));

    return {
      foundCount: allFoundVariables.length,
      totalCount: Math.max(staticAnalysis.totalCount, dynamicAnalysis.totalCount),
      foundVariables: allFoundVariables,
      missingVariables: staticAnalysis.missingVariables.filter(
        v => !allFoundVariables.includes(v)
      ),
      hasShopline: staticAnalysis.hasShopline || dynamicAnalysis.hasShopline,
      summary: `Hybrid detection: ${allFoundVariables.length} variables found`,

      // 合并增强字段
      platformVersion: dynamicAnalysis.platformVersion || staticAnalysis.platformVersion || 'Unknown',
      storeType: dynamicAnalysis.storeType || staticAnalysis.storeType || 'Unknown',
      confidence: Math.max(
        staticAnalysis.confidence || 0,
        dynamicAnalysis.confidence || 0
      ),
      totalDataSize: (staticAnalysis.totalDataSize || 0) + (dynamicAnalysis.totalDataSize || 0),
      completeness: Math.max(
        staticAnalysis.completeness || 0,
        dynamicAnalysis.completeness || 0
      ),
      detectionMethods: Array.from(new Set([
        ...(staticAnalysis.detectionMethods || []),
        ...(dynamicAnalysis.detectionMethods || [])
      ])),
      platformFeatures: Array.from(new Set([
        ...(staticAnalysis.platformFeatures || []),
        ...(dynamicAnalysis.platformFeatures || [])
      ])),
      recommendations: Array.from(new Set([
        ...(staticAnalysis.recommendations || []),
        ...(dynamicAnalysis.recommendations || [])
      ]))
    };
  }

  /**
   * 合并性能指标
   */
  private mergePerformanceMetrics(
    staticMetrics?: PerformanceMetrics,
    dynamicMetrics?: PerformanceMetrics
  ): PerformanceMetrics | undefined {
    if (!staticMetrics && !dynamicMetrics) return undefined;

    const result: PerformanceMetrics = {
      detectionStats: {
        strategiesUsed: [
          ...(staticMetrics?.detectionStats?.strategiesUsed || []),
          ...(dynamicMetrics?.detectionStats?.strategiesUsed || [])
        ],
        successfulDetections: (staticMetrics?.detectionStats?.successfulDetections || 0) +
                             (dynamicMetrics?.detectionStats?.successfulDetections || 0),
        averageConfidence: Math.max(
          staticMetrics?.detectionStats?.averageConfidence || 0,
          dynamicMetrics?.detectionStats?.averageConfidence || 0
        )
      }
    };

    if (staticMetrics?.httpMetrics) {
      result.httpMetrics = staticMetrics.httpMetrics;
    }

    if (staticMetrics?.parsingStats) {
      result.parsingStats = staticMetrics.parsingStats;
    }

    return result;
  }

  /**
   * 创建错误结果
   */
  private createErrorResult(url: string, method: any, error: string): UnifiedDetectionResult {
    return {
      success: false,
      method,
      url,
      timing: { total: 0, detection: 0, analysis: 0 },
      results: [],
      analysis: this.createEmptyAnalysis(),
      timestamp: new Date().toISOString(),
      error
    };
  }

  /**
   * 转换为增强分析格式
   */
  private convertToEnhancedAnalysis(analysis: any): EnhancedAnalysis {
    return {
      foundCount: analysis.foundCount,
      totalCount: analysis.totalCount,
      foundVariables: analysis.foundVariables,
      missingVariables: analysis.missingVariables,
      hasShopline: analysis.hasShopline,
      summary: analysis.summary,
      
      // 动态检测通常没有这些字段，使用默认值
      platformVersion: 'Unknown',
      storeType: analysis.hasShopline ? 'SHOPLINE Store' : 'Not a SHOPLINE store',
      confidence: analysis.foundCount / analysis.totalCount,
      totalDataSize: 0,
      completeness: analysis.foundCount / analysis.totalCount,
      detectionMethods: ['dynamic-evaluation'],
      platformFeatures: [],
      recommendations: []
    };
  }

  /**
   * 创建空的分析结果
   */
  private createEmptyAnalysis(): EnhancedAnalysis {
    return {
      foundCount: 0,
      totalCount: 4,
      foundVariables: [],
      missingVariables: ['Shopline', '__ENV__', 'mainConfig', 'shopline'],
      hasShopline: false,
      summary: 'No SHOPLINE variables detected',
      platformVersion: 'Unknown',
      storeType: 'Not a SHOPLINE store',
      confidence: 0,
      totalDataSize: 0,
      completeness: 0,
      detectionMethods: [],
      platformFeatures: [],
      recommendations: []
    };
  }
}

/**
 * 默认的混合检测服务实例
 */
export const hybridDetectionService = new HybridDetectionService();
