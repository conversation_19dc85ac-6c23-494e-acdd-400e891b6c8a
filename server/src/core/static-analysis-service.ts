/**
 * 静态分析服务
 * 封装静态分析器，提供统一的接口和结果格式转换
 */

import { StaticAnalyzer, StaticAnalysisResult } from '../static-analysis/static-analyzer';
import { cacheManager } from './cache';
import {
  UnifiedDetectionResult,
  EnhancedAnalysis,
  StaticAnalysisOptions,
  VariableResult,
  UnifiedTiming,
  PerformanceMetrics
} from '../types';
import { log } from '../config';

export class StaticAnalysisService {
  private analyzer: StaticAnalyzer;

  constructor(options: StaticAnalysisOptions = {}) {
    this.analyzer = new StaticAnalyzer({
      http: options.http || {},
      html: options.html || {},
      enableDetailedReport: options.enableDetailedReport || false,
      enablePerformanceMetrics: options.enablePerformanceMetrics || false
    });
  }

  /**
   * 执行静态分析并转换为统一格式
   */
  async analyze(url: string): Promise<UnifiedDetectionResult> {
    try {
      // 检查缓存
      const cachedResult = await cacheManager.getUnified(url, 'static-analysis');
      if (cachedResult) {
        log.info(`Static analysis cache hit for ${url}`);
        return cachedResult;
      }

      log.info(`Starting static analysis for ${url}`);

      const startTime = Date.now();
      const result = await this.analyzer.analyze(url);
      const totalTime = Date.now() - startTime;

      // 转换为统一格式
      const unifiedResult = this.convertToUnifiedFormat(result, totalTime);

      // 缓存结果
      if (unifiedResult.success) {
        await cacheManager.setUnified(url, 'static-analysis', unifiedResult);
      }

      log.info(`Static analysis completed for ${url}`, {
        success: unifiedResult.success,
        timing: unifiedResult.timing.total,
        foundVariables: unifiedResult.analysis.foundCount
      });

      return unifiedResult;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error(`Static analysis failed for ${url}`, { error: errorMessage });

      return {
        success: false,
        method: 'static-analysis',
        url,
        timing: {
          total: Date.now() - Date.now(),
          detection: 0,
          analysis: 0
        },
        results: [],
        analysis: this.createEmptyAnalysis(),
        timestamp: new Date().toISOString(),
        error: errorMessage
      };
    }
  }

  /**
   * 批量分析
   */
  async analyzeBatch(urls: string[]): Promise<UnifiedDetectionResult[]> {
    log.info(`Starting batch static analysis for ${urls.length} URLs`);

    const results: UnifiedDetectionResult[] = [];

    for (const url of urls) {
      try {
        const result = await this.analyze(url);
        results.push(result);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        results.push({
          success: false,
          method: 'static-analysis',
          url,
          timing: { total: 0, detection: 0, analysis: 0 },
          results: [],
          analysis: this.createEmptyAnalysis(),
          timestamp: new Date().toISOString(),
          error: errorMessage
        });
      }
    }

    return results;
  }

  /**
   * 获取分析器统计信息
   */
  getStats() {
    return this.analyzer.getStats();
  }

  /**
   * 重置分析器状态
   */
  reset() {
    this.analyzer.reset();
  }

  /**
   * 转换静态分析结果为统一格式
   */
  private convertToUnifiedFormat(
    result: StaticAnalysisResult, 
    totalTime: number,
    fallbackUrl?: string
  ): UnifiedDetectionResult {
    if (!result.success) {
      return {
        success: false,
        method: 'static-analysis',
        url: fallbackUrl || result.url,
        timing: {
          total: totalTime || result.timing.total,
          detection: 0,
          analysis: 0
        },
        results: [],
        analysis: this.createEmptyAnalysis(),
        timestamp: new Date().toISOString(),
        error: result.error || 'Static analysis failed'
      };
    }

    // 转换变量结果
    const results: VariableResult[] = this.convertVariableResults(result);

    // 转换分析结果
    const analysis: EnhancedAnalysis = this.convertAnalysis(result);

    // 转换时间信息
    const timing: UnifiedTiming = {
      total: totalTime || result.timing.total,
      detection: result.timing.variableDetection,
      analysis: result.timing.resultAnalysis,
      breakdown: {
        httpFetch: result.timing.httpFetch,
        htmlParsing: result.timing.htmlParsing,
        variableDetection: result.timing.variableDetection,
        resultAnalysis: result.timing.resultAnalysis
      }
    };

    // 转换性能指标
    const performanceMetrics: PerformanceMetrics | undefined = 
      result.performanceMetrics ? {
        httpMetrics: result.performanceMetrics.httpMetrics,
        parsingStats: result.performanceMetrics.parsingStats,
        detectionStats: result.performanceMetrics.detectionStats
      } : undefined;

    const unifiedResult: UnifiedDetectionResult = {
      success: true,
      method: 'static-analysis',
      url: result.url,
      timing,
      results,
      analysis,
      timestamp: new Date().toISOString()
    };

    if (performanceMetrics) {
      unifiedResult.performanceMetrics = performanceMetrics;
    }

    return unifiedResult;
  }

  /**
   * 转换变量检测结果
   */
  private convertVariableResults(result: StaticAnalysisResult): VariableResult[] {
    // 从静态分析结果中提取变量信息
    const foundVariables = result.analysis.foundVariables;
    const variableResults: VariableResult[] = [];

    // 为每个找到的变量创建结果
    foundVariables.forEach(varName => {
      variableResults.push({
        name: varName,
        found: true,
        value: `[Static Analysis] Variable detected`,
        type: 'object',
        size: Math.floor(result.analysis.totalDataSize / foundVariables.length) || 0
      });
    });

    // 添加未找到的默认变量
    const defaultVars = ['Shopline', '__ENV__', 'mainConfig', 'shopline'];
    defaultVars.forEach(varName => {
      if (!foundVariables.includes(varName)) {
        variableResults.push({
          name: varName,
          found: false,
          value: null,
          type: 'undefined',
          size: 0
        });
      }
    });

    return variableResults;
  }

  /**
   * 转换分析结果
   */
  private convertAnalysis(result: StaticAnalysisResult): EnhancedAnalysis {
    const analysis = result.analysis;
    
    return {
      // 基础字段
      foundCount: analysis.totalVariablesFound,
      totalCount: analysis.foundVariables.length + 
        (['Shopline', '__ENV__', 'mainConfig', 'shopline'].length - analysis.foundVariables.length),
      foundVariables: analysis.foundVariables,
      missingVariables: ['Shopline', '__ENV__', 'mainConfig', 'shopline']
        .filter(v => !analysis.foundVariables.includes(v)),
      hasShopline: analysis.isShoplineStore,
      summary: `${analysis.storeType} - ${analysis.foundVariables.length} variables detected`,
      
      // 增强字段
      platformVersion: analysis.platformVersion,
      storeType: analysis.storeType,
      confidence: analysis.confidence,
      totalDataSize: analysis.totalDataSize,
      completeness: analysis.completeness,
      detectionMethods: analysis.detectionMethods,
      platformFeatures: analysis.platformFeatures,
      recommendations: analysis.recommendations
    };
  }

  /**
   * 创建空的分析结果
   */
  private createEmptyAnalysis(): EnhancedAnalysis {
    return {
      foundCount: 0,
      totalCount: 4,
      foundVariables: [],
      missingVariables: ['Shopline', '__ENV__', 'mainConfig', 'shopline'],
      hasShopline: false,
      summary: 'No SHOPLINE variables detected',
      platformVersion: 'Unknown',
      storeType: 'Not a SHOPLINE store',
      confidence: 0,
      totalDataSize: 0,
      completeness: 0,
      detectionMethods: [],
      platformFeatures: [],
      recommendations: ['Unable to analyze - check URL accessibility']
    };
  }
}
