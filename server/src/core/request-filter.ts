/**
 * 请求过滤器 - 处理第三方分析服务的网络请求
 * 减少日志噪音，提高检测性能
 */

import { log } from '../config';

/**
 * 已知的第三方分析服务域名
 */
const ANALYTICS_DOMAINS = [
  // Google Analytics
  'analytics.google.com',
  'google-analytics.com',
  'googletagmanager.com',
  'doubleclick.net',
  
  // SHOPLINE Analytics
  'events.shoplytics.com',
  'shoplytics.com',
  
  // Facebook/Meta
  'facebook.com',
  'connect.facebook.net',
  
  // 其他常见分析服务
  'hotjar.com',
  'mixpanel.com',
  'segment.com',
  'amplitude.com',
  'intercom.io',
  'zendesk.com',
  'tawk.to',
  'crisp.chat',
  
  // 广告和追踪
  'googlesyndication.com',
  'googleadservices.com',
  'adsystem.com',
  'amazon-adsystem.com'
];

/**
 * 已知的分析服务路径模式
 */
const ANALYTICS_PATTERNS = [
  /\/collect\?/,           // Google Analytics collect
  /\/api\/v\d+\/event/,    // 事件追踪API
  /\/pixel\?/,             // Facebook Pixel
  /\/track\?/,             // 通用追踪
  /\/analytics\//,         // 分析服务
  /\/gtm\.js/,             // Google Tag Manager
  /\/ga\.js/,              // Google Analytics
  /\/fbevents\.js/,        // Facebook Events
];

/**
 * 请求过滤器类
 */
export class RequestFilter {
  private blockedDomains: Set<string>;
  private allowedPatterns: RegExp[];
  private stats: {
    totalRequests: number;
    blockedRequests: number;
    analyticsBlocked: number;
  };

  constructor() {
    this.blockedDomains = new Set(ANALYTICS_DOMAINS);
    this.allowedPatterns = ANALYTICS_PATTERNS;
    this.stats = {
      totalRequests: 0,
      blockedRequests: 0,
      analyticsBlocked: 0
    };
  }

  /**
   * 检查URL是否应该被过滤
   */
  shouldFilterRequest(url: string): {
    shouldFilter: boolean;
    reason?: string;
    category?: 'analytics' | 'ads' | 'tracking';
  } {
    this.stats.totalRequests++;

    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.toLowerCase();
      const path = urlObj.pathname + urlObj.search;

      // 检查域名
      for (const blockedDomain of this.blockedDomains) {
        if (domain === blockedDomain || domain.endsWith('.' + blockedDomain)) {
          this.stats.blockedRequests++;
          this.stats.analyticsBlocked++;
          
          return {
            shouldFilter: true,
            reason: `Analytics domain: ${blockedDomain}`,
            category: this.categorizeRequest(url)
          };
        }
      }

      // 检查路径模式
      for (const pattern of this.allowedPatterns) {
        if (pattern.test(path)) {
          this.stats.blockedRequests++;
          this.stats.analyticsBlocked++;
          
          return {
            shouldFilter: true,
            reason: `Analytics pattern: ${pattern.source}`,
            category: this.categorizeRequest(url)
          };
        }
      }

      return { shouldFilter: false };

    } catch (error) {
      // URL解析失败，不过滤
      return { shouldFilter: false };
    }
  }

  /**
   * 分类请求类型
   */
  private categorizeRequest(url: string): 'analytics' | 'ads' | 'tracking' {
    const lowerUrl = url.toLowerCase();
    
    if (lowerUrl.includes('analytics') || lowerUrl.includes('collect') || lowerUrl.includes('event')) {
      return 'analytics';
    }
    
    if (lowerUrl.includes('ads') || lowerUrl.includes('doubleclick') || lowerUrl.includes('googlesyndication')) {
      return 'ads';
    }
    
    return 'tracking';
  }

  /**
   * 检查是否应该记录警告日志
   */
  shouldLogWarning(url: string, error: string): boolean {
    const filterResult = this.shouldFilterRequest(url);
    
    if (filterResult.shouldFilter) {
      // 对于已知的分析服务，降级为INFO级别（因为log没有debug方法）
      log.info('Third-party analytics request failed (expected)', {
        url,
        error,
        category: filterResult.category,
        reason: filterResult.reason
      });
      return false;
    }
    
    // 对于其他请求，保持WARN级别
    return true;
  }

  /**
   * 添加自定义过滤域名
   */
  addBlockedDomain(domain: string): void {
    this.blockedDomains.add(domain.toLowerCase());
    log.info('Added blocked domain', { domain });
  }

  /**
   * 移除过滤域名
   */
  removeBlockedDomain(domain: string): void {
    this.blockedDomains.delete(domain.toLowerCase());
    log.info('Removed blocked domain', { domain });
  }

  /**
   * 获取过滤统计
   */
  getStats() {
    return {
      ...this.stats,
      filterRate: this.stats.totalRequests > 0 
        ? (this.stats.blockedRequests / this.stats.totalRequests * 100).toFixed(1) + '%'
        : '0%',
      blockedDomains: Array.from(this.blockedDomains).sort()
    };
  }

  /**
   * 重置统计
   */
  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      blockedRequests: 0,
      analyticsBlocked: 0
    };
  }

  /**
   * 配置浏览器请求拦截
   */
  async setupBrowserInterception(page: any, options: {
    blockAnalytics?: boolean;
    blockAds?: boolean;
    logBlocked?: boolean;
  } = {}) {
    const { blockAnalytics = false, blockAds = false, logBlocked = false } = options;

    await page.route('**/*', (route: any) => {
      const url = route.request().url();
      const filterResult = this.shouldFilterRequest(url);

      if (filterResult.shouldFilter) {
        const shouldBlock = 
          (blockAnalytics && filterResult.category === 'analytics') ||
          (blockAds && filterResult.category === 'ads') ||
          (filterResult.category === 'tracking');

        if (shouldBlock) {
          if (logBlocked) {
            log.info('Blocked request', {
              url,
              category: filterResult.category,
              reason: filterResult.reason
            });
          }
          
          // 阻止请求
          route.abort();
          return;
        }
      }

      // 继续请求
      route.continue();
    });
  }
}

// 导出单例实例
export const requestFilter = new RequestFilter();

/**
 * 请求过滤配置选项
 */
export interface RequestFilterOptions {
  enableFiltering?: boolean;
  blockAnalytics?: boolean;
  blockAds?: boolean;
  logBlocked?: boolean;
  customBlockedDomains?: string[];
}

/**
 * 应用请求过滤配置
 */
export function applyRequestFilterConfig(options: RequestFilterOptions) {
  if (options.customBlockedDomains) {
    options.customBlockedDomains.forEach(domain => {
      requestFilter.addBlockedDomain(domain);
    });
  }

  log.info('Request filter configured', {
    enableFiltering: options.enableFiltering,
    blockAnalytics: options.blockAnalytics,
    blockAds: options.blockAds,
    customDomains: options.customBlockedDomains?.length || 0
  });
}
