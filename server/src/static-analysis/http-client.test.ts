/**
 * HTTP客户端模块单元测试
 *
 * 测试覆盖范围：
 * - HTTP请求和响应处理
 * - 错误处理和重试机制
 * - 超时和性能监控
 * - 请求头和内容类型验证
 * - 边界条件和异常情况
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { HttpClient, HttpClientError } from './http-client';

// Mock fetch for HTTP testing
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('HttpClient', () => {
  let httpClient: HttpClient;

  beforeEach(() => {
    httpClient = new HttpClient();
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  /**
   * 创建模拟的HTTP响应
   * @param html - HTML内容
   * @param status - HTTP状态码
   * @param headers - 响应头
   * @returns 模拟的Response对象
   */
  const createMockResponse = (
    html: string,
    status = 200,
    headers: Record<string, string> = {}
  ) => ({
    ok: status >= 200 && status < 300,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    headers: new Map(Object.entries({
      'content-type': 'text/html; charset=utf-8',
      ...headers
    })),
    text: () => Promise.resolve(html)
  });

  describe('fetchPage', () => {
    it('应该成功获取HTML页面', async () => {
      const mockHtml = '<html><head><title>Test</title></head><body>Test content</body></html>';

      mockFetch.mockResolvedValueOnce(createMockResponse(mockHtml, 200, {
        'content-length': mockHtml.length.toString()
      }));

      const result = await httpClient.fetchPage('https://example.com');

      expect(result).toMatchObject({
        html: mockHtml,
        size: mockHtml.length,
        statusCode: 200,
        timing: expect.any(Number)
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'https://example.com',
        expect.objectContaining({
          headers: expect.objectContaining({
            'User-Agent': expect.stringContaining('Mozilla'),
            'Accept': expect.stringContaining('text/html')
          })
        })
      );
    });

    it('应该处理HTTP错误状态码', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found',
        headers: new Map()
      };

      mockFetch.mockResolvedValue(mockResponse);

      await expect(httpClient.fetchPage('https://example.com/notfound'))
        .rejects
        .toThrow(HttpClientError);

      try {
        await httpClient.fetchPage('https://example.com/notfound');
      } catch (error) {
        expect(error).toBeInstanceOf(HttpClientError);
        expect(error.statusCode).toBe(404);
        expect(error.message).toContain('404');
      }
    });

    it('应该处理网络超时', async () => {
      const abortError = new Error('AbortError');
      abortError.name = 'AbortError';

      mockFetch.mockRejectedValue(abortError);

      const client = new HttpClient({ timeout: 1000, maxRetries: 0 });

      await expect(client.fetchPage('https://slow-example.com'))
        .rejects
        .toThrow(HttpClientError);
    });

    it('应该验证URL格式', async () => {
      await expect(httpClient.fetchPage('invalid-url'))
        .rejects
        .toThrow(HttpClientError);

      await expect(httpClient.fetchPage('ftp://example.com'))
        .rejects
        .toThrow(HttpClientError);
    });

    it('应该阻止私有IP访问', async () => {
      const privateIPs = [
        'http://localhost',
        'http://127.0.0.1',
        'http://***********',
        'http://********',
        'http://**********'
      ];

      for (const url of privateIPs) {
        await expect(httpClient.fetchPage(url))
          .rejects
          .toThrow(HttpClientError);
      }
    });

    it('应该检查内容类型', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'application/json']]),
        text: () => Promise.resolve('{"data": "json"}')
      };

      mockFetch.mockResolvedValueOnce(mockResponse);

      await expect(httpClient.fetchPage('https://api.example.com'))
        .rejects
        .toThrow(HttpClientError);
    });

    it('应该限制响应大小', async () => {
      const largeHtml = 'x'.repeat(11 * 1024 * 1024); // 11MB
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(largeHtml)
      };

      mockFetch.mockResolvedValueOnce(mockResponse);

      await expect(httpClient.fetchPage('https://large.example.com'))
        .rejects
        .toThrow(HttpClientError);
    });

    it('应该处理网络错误', async () => {
      const networkError = new Error('Network error');
      mockFetch.mockRejectedValue(networkError);

      const client = new HttpClient({ maxRetries: 0 }); // 禁用重试简化测试

      await expect(client.fetchPage('https://broken.example.com'))
        .rejects
        .toThrow(HttpClientError);
    });
  });

  describe('metrics', () => {
    it('应该正确跟踪成功请求指标', async () => {
      const mockHtml = '<html>Test</html>';
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(mockHtml)
      };

      mockFetch.mockResolvedValue(mockResponse);

      await httpClient.fetchPage('https://example1.com');
      await httpClient.fetchPage('https://example2.com');

      const metrics = httpClient.getMetrics();
      
      expect(metrics.totalRequests).toBe(2);
      expect(metrics.successfulRequests).toBe(2);
      expect(metrics.failedRequests).toBe(0);
      expect(metrics.totalDataTransferred).toBe(mockHtml.length * 2);
      expect(httpClient.getSuccessRate()).toBe(100);
    });

    it('应该正确跟踪失败请求指标', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      const client = new HttpClient({ maxRetries: 0 });

      try {
        await client.fetchPage('https://broken.example.com');
      } catch (error) {
        // 预期的错误
      }

      const metrics = client.getMetrics();
      
      expect(metrics.totalRequests).toBe(1);
      expect(metrics.successfulRequests).toBe(0);
      expect(metrics.failedRequests).toBe(1);
      expect(client.getSuccessRate()).toBe(0);
    });

    it('应该能够重置指标', () => {
      const client = new HttpClient();
      
      // 模拟一些指标数据
      client['metrics'] = {
        totalRequests: 10,
        successfulRequests: 8,
        failedRequests: 2,
        avgResponseTime: 1500,
        totalDataTransferred: 1024000
      };

      client.resetMetrics();

      const metrics = client.getMetrics();
      expect(metrics.totalRequests).toBe(0);
      expect(metrics.successfulRequests).toBe(0);
      expect(metrics.failedRequests).toBe(0);
      expect(metrics.avgResponseTime).toBe(0);
      expect(metrics.totalDataTransferred).toBe(0);
    });
  });

  describe('configuration', () => {
    it('应该使用自定义配置', async () => {
      const customClient = new HttpClient({
        timeout: 5000,
        userAgent: 'Custom Bot 1.0',
        maxRetries: 3
      });

      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve('<html>Test</html>')
      };

      mockFetch.mockResolvedValueOnce(mockResponse);

      await customClient.fetchPage('https://example.com');

      expect(mockFetch).toHaveBeenCalledWith(
        'https://example.com',
        expect.objectContaining({
          headers: expect.objectContaining({
            'User-Agent': 'Custom Bot 1.0'
          })
        })
      );
    });

    it('应该支持请求级别的配置覆盖', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve('<html>Test</html>')
      };

      mockFetch.mockResolvedValueOnce(mockResponse);

      await httpClient.fetchPage('https://example.com', {
        userAgent: 'Override Bot 1.0'
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'https://example.com',
        expect.objectContaining({
          headers: expect.objectContaining({
            'User-Agent': 'Override Bot 1.0'
          })
        })
      );
    });
  });
});
