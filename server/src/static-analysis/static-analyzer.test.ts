/**
 * 静态分析器单元测试
 *
 * 测试覆盖范围：
 * - 完整的分析流程（HTTP获取、HTML解析、变量检测、结果分析）
 * - 不同平台版本的识别（1.0, 2.0, 混合版本）
 * - 错误处理和边界条件
 * - 性能计时和统计信息
 * - 批量分析功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { StaticAnalyzer, StaticAnalysisError } from './static-analyzer';

// Mock fetch for testing
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('StaticAnalyzer', () => {
  let analyzer: StaticAnalyzer;

  beforeEach(() => {
    analyzer = new StaticAnalyzer();
    vi.clearAllMocks();
  });

  /**
   * 创建模拟的HTTP响应
   * @param html - HTML内容
   * @param status - HTTP状态码
   * @param contentType - 内容类型
   * @returns 模拟的Response对象
   */
  const createMockResponse = (html: string, status = 200, contentType = 'text/html') => ({
    ok: status >= 200 && status < 300,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    headers: new Map([['content-type', contentType]]),
    text: () => Promise.resolve(html)
  });

  describe('单个URL分析', () => {
    describe('SHOPLINE平台识别', () => {
      it('应该成功分析SHOPLINE 1.0网站', async () => {
        const mockHtml = `
          <html>
            <head>
              <script>
                window.mainConfig = {
                  "merchantId": "test123",
                  "storeId": "store456",
                  "theme": "default"
                };
                window.shopline = {
                  "version": "1.0",
                  "features": ["cart", "checkout"]
                };
              </script>
            </head>
            <body>SHOPLINE Store Content</body>
          </html>
        `;

        mockFetch.mockResolvedValueOnce(createMockResponse(mockHtml));

        const result = await analyzer.analyze('https://test-store.shoplineapp.com');

        // 基本结果验证
        expect(result.success).toBe(true);
        expect(result.method).toBe('static-analysis');
        expect(result.url).toBe('https://test-store.shoplineapp.com');

        // 平台识别验证
        expect(result.analysis.isShoplineStore).toBe(true);
        expect(result.analysis.platformVersion).toBe('1.0');
        expect(result.analysis.storeType).toBe('SHOPLINE 1.0 Store');
        expect(result.analysis.foundVariables).toContain('mainConfig');
        expect(result.analysis.foundVariables).toContain('shopline');
        expect(result.analysis.confidence).toBeGreaterThan(0.8);

        // 性能计时验证
        expect(result.timing.total).toBeGreaterThanOrEqual(0);
        expect(result.timing.httpFetch).toBeGreaterThanOrEqual(0);
        expect(result.timing.htmlParsing).toBeGreaterThanOrEqual(0);
        expect(result.timing.variableDetection).toBeGreaterThanOrEqual(0);
        expect(result.timing.resultAnalysis).toBeGreaterThanOrEqual(0);
      });

      it('应该成功分析SHOPLINE 2.0网站', async () => {
        const mockHtml = `
          <html>
            <head>
              <script>
                window.Shopline = {
                  "store": {"id": "store789"},
                  "version": "2.0",
                  "api": {"endpoint": "https://api.shopline.com"}
                };
                window.__ENV__ = {
                  "APP_ENV": "production",
                  "API_VERSION": "v2"
                };
              </script>
            </head>
            <body>Modern SHOPLINE Store</body>
          </html>
        `;

        mockFetch.mockResolvedValueOnce(createMockResponse(mockHtml));

        const result = await analyzer.analyze('https://test-store.myshopline.com');

        // 基本结果验证
        expect(result.success).toBe(true);
        expect(result.method).toBe('static-analysis');

        // 平台识别验证
        expect(result.analysis.isShoplineStore).toBe(true);
        expect(result.analysis.platformVersion).toBe('2.0');
        expect(result.analysis.storeType).toBe('SHOPLINE 2.0 Store');
        expect(result.analysis.foundVariables).toContain('Shopline');
        expect(result.analysis.foundVariables).toContain('__ENV__');
        expect(result.analysis.confidence).toBeGreaterThan(0.9);
        expect(result.analysis.platformFeatures).toContain('modern-api');
      });

    it('应该识别非SHOPLINE网站', async () => {
      const mockHtml = `
        <html>
          <head>
            <script>
              var someOtherConfig = {"notShopline": true};
            </script>
          </head>
          <body>Regular website content</body>
        </html>
      `;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(mockHtml)
      });

      const result = await analyzer.analyze('https://regular-website.com');

      expect(result.success).toBe(true);
      expect(result.analysis.isShoplineStore).toBe(false);
      expect(result.analysis.platformVersion).toBe('N/A');
      expect(result.analysis.storeType).toBe('Not a SHOPLINE Store');
      expect(result.analysis.confidence).toBe(0);
      expect(result.analysis.foundVariables).toHaveLength(0);
      });
    });

    describe('错误处理', () => {
      it('应该处理HTTP错误', async () => {
        mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        headers: new Map()
      });

      await expect(analyzer.analyze('https://nonexistent.com'))
        .rejects
        .toThrow(StaticAnalysisError);

      try {
        await analyzer.analyze('https://nonexistent.com');
      } catch (error) {
        expect(error).toBeInstanceOf(StaticAnalysisError);
        expect(error.stage).toBe('http');
      }
      });

    it('应该处理网络错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(analyzer.analyze('https://unreachable.com'))
        .rejects
        .toThrow(StaticAnalysisError);
      });
    });

    describe('配置选项', () => {
      it('应该支持详细报告选项', async () => {
      const analyzerWithReport = new StaticAnalyzer({ enableDetailedReport: true });
      
      const mockHtml = `
        <html>
          <script>window.mainConfig = {"test": true};</script>
        </html>
      `;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(mockHtml)
      });

      const result = await analyzerWithReport.analyze('https://test.com');

      expect(result.detailedReport).toBeDefined();
      expect(result.detailedReport).toContain('SHOPLINE STORE ANALYSIS REPORT');
    });

    it('应该支持性能指标选项', async () => {
      const analyzerWithMetrics = new StaticAnalyzer({ enablePerformanceMetrics: true });
      
      const mockHtml = `
        <html>
          <script>window.mainConfig = {"test": true};</script>
        </html>
      `;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(mockHtml)
      });

      const result = await analyzerWithMetrics.analyze('https://test.com');

      expect(result.performanceMetrics).toBeDefined();
      expect(result.performanceMetrics?.httpMetrics).toBeDefined();
      expect(result.performanceMetrics?.parsingStats).toBeDefined();
      expect(result.performanceMetrics?.detectionStats).toBeDefined();
      });
    });
  });

  describe('analyzeMultiple', () => {
    it('应该批量分析多个URL', async () => {
      const mockHtml1 = '<html><head><script>window.mainConfig = {"id": "1"}; window.shopline = {"version": "1.0"};</script></head></html>';
      const mockHtml2 = '<html><head><script>window.Shopline = {"id": "2"}; window.__ENV__ = {"APP_ENV": "production"};</script></head></html>';

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Map([['content-type', 'text/html']]),
          text: () => Promise.resolve(mockHtml1)
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Map([['content-type', 'text/html']]),
          text: () => Promise.resolve(mockHtml2)
        });

      const urls = ['https://store1.com', 'https://store2.com'];
      const results = await analyzer.analyzeMultiple(urls);

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(true);
      expect(results[0].analysis.platformVersion).toBe('1.0');
      expect(results[1].analysis.platformVersion).toBe('2.0');
    });

    it('应该处理批量分析中的部分失败', async () => {
      const mockHtml = '<html><script>window.mainConfig = {"test": true};</script></html>';

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Map([['content-type', 'text/html']]),
          text: () => Promise.resolve(mockHtml)
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 404,
          statusText: 'Not Found',
          headers: new Map()
        });

      const urls = ['https://working.com', 'https://broken.com'];
      const results = await analyzer.analyzeMultiple(urls);

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
      expect(results[1].error).toBeDefined();
      expect(results[1].analysis.storeType).toBe('Analysis Failed');
    });
  });

  describe('validateUrl', () => {
    it('应该验证有效的URL', () => {
      const validUrls = [
        'https://example.com',
        'http://test.shoplineapp.com',
        'https://store.myshopline.com/products'
      ];

      validUrls.forEach(url => {
        const result = analyzer.validateUrl(url);
        expect(result.valid).toBe(true);
        expect(result.reason).toBeUndefined();
      });
    });

    it('应该拒绝无效的URL', () => {
      const invalidUrls = [
        'not-a-url',
        'ftp://example.com',
        'javascript:alert(1)',
        ''
      ];

      invalidUrls.forEach(url => {
        const result = analyzer.validateUrl(url);
        expect(result.valid).toBe(false);
        expect(result.reason).toBeDefined();
      });
    });

    it('应该拒绝私有IP地址', () => {
      const privateIPs = [
        'http://localhost',
        'http://127.0.0.1',
        'http://***********',
        'http://********',
        'http://**********'
      ];

      privateIPs.forEach(url => {
        const result = analyzer.validateUrl(url);
        expect(result.valid).toBe(false);
        expect(result.reason).toContain('Private IP');
      });
    });
  });

  describe('generateSummary', () => {
    it('应该生成正确的分析摘要', () => {
      const mockResults = [
        {
          success: true,
          method: 'static-analysis' as const,
          url: 'https://store1.com',
          timing: { total: 1000, httpFetch: 200, htmlParsing: 100, variableDetection: 50, resultAnalysis: 50 },
          analysis: {
            isShoplineStore: true,
            platformVersion: '1.0',
            storeType: 'SHOPLINE 1.0 Store',
            confidence: 0.9,
            foundVariables: ['mainConfig'],
            totalVariablesFound: 1,
            totalDataSize: 100,
            completeness: 0.8,
            detectionMethods: ['direct-assignment'],
            analysisTimestamp: new Date().toISOString(),
            platformFeatures: ['legacy-api'],
            recommendations: ['Consider upgrading to 2.0']
          }
        },
        {
          success: true,
          method: 'static-analysis' as const,
          url: 'https://store2.com',
          timing: { total: 1200, httpFetch: 300, htmlParsing: 150, variableDetection: 75, resultAnalysis: 75 },
          analysis: {
            isShoplineStore: true,
            platformVersion: '2.0',
            storeType: 'SHOPLINE 2.0 Store',
            confidence: 0.95,
            foundVariables: ['Shopline', '__ENV__'],
            totalVariablesFound: 2,
            totalDataSize: 200,
            completeness: 0.9,
            detectionMethods: ['direct-assignment'],
            analysisTimestamp: new Date().toISOString(),
            platformFeatures: ['modern-api'],
            recommendations: ['Modern platform detected']
          }
        },
        {
          success: false,
          method: 'static-analysis' as const,
          url: 'https://broken.com',
          timing: { total: 0, httpFetch: 0, htmlParsing: 0, variableDetection: 0, resultAnalysis: 0 },
          analysis: {
            isShoplineStore: false,
            platformVersion: 'N/A',
            storeType: 'Analysis Failed',
            confidence: 0,
            foundVariables: [],
            totalVariablesFound: 0,
            totalDataSize: 0,
            completeness: 0,
            detectionMethods: [],
            analysisTimestamp: new Date().toISOString(),
            platformFeatures: [],
            recommendations: ['Analysis failed']
          },
          error: 'Network error'
        }
      ];

      const summary = analyzer.generateSummary(mockResults);

      expect(summary.totalAnalyzed).toBe(3);
      expect(summary.successfulAnalyses).toBe(2);
      expect(summary.shoplineStores).toBe(2);
      expect(summary.platformVersions['1.0']).toBe(1);
      expect(summary.platformVersions['2.0']).toBe(1);
      expect(summary.avgConfidence).toBe(0.93); // (0.9 + 0.95) / 2 = 0.925, rounded to 0.93
      expect(summary.avgResponseTime).toBe(1100); // (1000 + 1200) / 2
      expect(summary.recommendations).toContain('Consider upgrading to 2.0');
      expect(summary.recommendations).toContain('Modern platform detected');
    });
  });

  describe('getStats and reset', () => {
    it('应该跟踪和重置统计信息', async () => {
      const mockHtml = '<html><script>window.mainConfig = {"test": true};</script></html>';

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(mockHtml)
      });

      // 执行一些分析
      await analyzer.analyze('https://test1.com');
      await analyzer.analyze('https://test2.com');

      const stats = analyzer.getStats();
      expect(stats.totalAnalyses).toBe(2);
      expect(stats.successRate).toBe(100);

      // 重置统计
      analyzer.reset();
      const resetStats = analyzer.getStats();
      expect(resetStats.totalAnalyses).toBe(0);
    });
  });
});
