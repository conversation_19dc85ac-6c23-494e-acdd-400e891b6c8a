/**
 * 结果分析引擎单元测试
 *
 * 测试覆盖范围：
 * - 平台版本识别（1.0, 2.0, 混合版本）
 * - 置信度计算和数据完整性评估
 * - 建议生成系统
 * - 详细报告生成
 * - 边界条件和错误处理
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { ResultAnalysisEngine } from './result-analyzer';
import type { VariableDetectionResult } from './variable-detector';

describe('ResultAnalysisEngine', () => {
  let analyzer: ResultAnalysisEngine;

  beforeEach(() => {
    analyzer = new ResultAnalysisEngine();
  });

  /**
   * 创建测试用的检测结果辅助函数
   * @param name - 变量名称
   * @param found - 是否找到
   * @param confidence - 置信度
   * @param method - 检测方法
   * @param size - 数据大小
   * @returns 标准化的检测结果对象
   */
  const createDetectionResult = (
    name: string,
    found: boolean = true,
    confidence: number = 0.9,
    method: string = 'direct-assignment',
    size: number = 100
  ): VariableDetectionResult => ({
    name,
    found,
    detectionMethod: method,
    confidence,
    size
  });

  describe('平台版本识别', () => {
    describe('SHOPLINE 1.0 识别', () => {
      it('应该正确识别完整的SHOPLINE 1.0商店', () => {
        const results = [
          createDetectionResult('mainConfig', true, 0.95),
          createDetectionResult('shopline', true, 0.90),
          createDetectionResult('Shopline', false),
          createDetectionResult('__ENV__', false)
        ];

        const analysis = analyzer.analyzeDetectionResults(results);

        expect(analysis.isShoplineStore).toBe(true);
        expect(analysis.platformVersion).toBe('1.0');
        expect(analysis.storeType).toBe('SHOPLINE 1.0 Store');
        expect(analysis.confidence).toBeGreaterThan(0.8);
        expect(analysis.foundVariables).toEqual(['mainConfig', 'shopline']);
        expect(analysis.platformFeatures).toContain('legacy-api');
        expect(analysis.platformFeatures).toContain('basic-checkout');
        expect(analysis.completeness).toBe(1.0); // 100% 完整性
      });

      it('应该识别部分SHOPLINE 1.0商店', () => {
        const results = [
          createDetectionResult('mainConfig', true, 0.85),
          createDetectionResult('shopline', false),
          createDetectionResult('Shopline', false),
          createDetectionResult('__ENV__', false)
        ];

        const analysis = analyzer.analyzeDetectionResults(results);

        expect(analysis.isShoplineStore).toBe(true);
        expect(analysis.platformVersion).toBe('Unknown'); // 单个变量无法确定版本
        expect(analysis.foundVariables).toEqual(['mainConfig']);
        expect(analysis.completeness).toBe(0.25); // 25% 完整性（1/4变量）
      });
    });

    describe('SHOPLINE 2.0 识别', () => {
      it('应该正确识别完整的SHOPLINE 2.0商店', () => {
        const results = [
          createDetectionResult('Shopline', true, 0.95),
          createDetectionResult('__ENV__', true, 0.93),
          createDetectionResult('mainConfig', false),
          createDetectionResult('shopline', false)
        ];

        const analysis = analyzer.analyzeDetectionResults(results);

        expect(analysis.isShoplineStore).toBe(true);
        expect(analysis.platformVersion).toBe('2.0');
        expect(analysis.storeType).toBe('SHOPLINE 2.0 Store');
        expect(analysis.confidence).toBeGreaterThan(0.9);
        expect(analysis.foundVariables).toEqual(['Shopline', '__ENV__']);
        expect(analysis.platformFeatures).toContain('modern-api');
        expect(analysis.platformFeatures).toContain('enhanced-analytics');
        expect(analysis.platformFeatures).toContain('multi-currency');
        expect(analysis.completeness).toBe(1.0); // 100% 完整性
      });

      it('应该识别部分SHOPLINE 2.0商店', () => {
        const results = [
          createDetectionResult('Shopline', true, 0.90),
          createDetectionResult('__ENV__', false),
          createDetectionResult('mainConfig', false),
          createDetectionResult('shopline', false)
        ];

        const analysis = analyzer.analyzeDetectionResults(results);

        expect(analysis.isShoplineStore).toBe(true);
        expect(analysis.platformVersion).toBe('Unknown'); // 单个变量无法确定版本
        expect(analysis.foundVariables).toEqual(['Shopline']);
        expect(analysis.completeness).toBe(0.25); // 25% 完整性（1/4变量）
      });
    });

    describe('混合版本和特殊情况', () => {
      it('应该识别混合版本商店', () => {
      const results = [
        createDetectionResult('mainConfig', true, 0.90),
        createDetectionResult('Shopline', true, 0.92),
        createDetectionResult('__ENV__', true, 0.88),
        createDetectionResult('shopline', false)
      ];

      const analysis = analyzer.analyzeDetectionResults(results);

      expect(analysis.isShoplineStore).toBe(true);
      expect(analysis.platformVersion).toBe('Mixed');
      expect(analysis.storeType).toBe('SHOPLINE Mixed Version Store');
      expect(analysis.platformFeatures).toContain('hybrid-setup');
      expect(analysis.platformFeatures).toContain('migration-in-progress');
      });

      it('应该识别部分检测的商店', () => {
      const results = [
        createDetectionResult('Shopline', true, 0.85),
        createDetectionResult('mainConfig', false),
        createDetectionResult('__ENV__', false),
        createDetectionResult('shopline', false)
      ];

      const analysis = analyzer.analyzeDetectionResults(results);

      expect(analysis.isShoplineStore).toBe(true);
      expect(analysis.platformVersion).toBe('Unknown');
      expect(analysis.storeType).toBe('SHOPLINE Store (Partial Detection)');
      expect(analysis.confidence).toBeLessThan(0.8);
      expect(analysis.platformFeatures).toContain('partial-detection');
      });

      it('应该识别非SHOPLINE网站', () => {
      const results = [
        createDetectionResult('mainConfig', false),
        createDetectionResult('Shopline', false),
        createDetectionResult('__ENV__', false),
        createDetectionResult('shopline', false)
      ];

      const analysis = analyzer.analyzeDetectionResults(results);

      expect(analysis.isShoplineStore).toBe(false);
      expect(analysis.platformVersion).toBe('N/A');
      expect(analysis.storeType).toBe('Not a SHOPLINE Store');
      expect(analysis.confidence).toBe(0);
      expect(analysis.foundVariables).toHaveLength(0);
      });
    });
  });

  describe('数据完整性和置信度计算', () => {
    it('应该计算正确的数据完整性', () => {
      // SHOPLINE 2.0 with all variables
      const completeResults = [
        createDetectionResult('Shopline', true, 0.95),
        createDetectionResult('__ENV__', true, 0.93)
      ];

      const completeAnalysis = analyzer.analyzeDetectionResults(completeResults);
      expect(completeAnalysis.completeness).toBe(1.0);

      // SHOPLINE 2.0 with partial variables
      const partialResults = [
        createDetectionResult('Shopline', true, 0.95),
        createDetectionResult('__ENV__', false),
        createDetectionResult('mainConfig', false),
        createDetectionResult('shopline', false)
      ];

      const partialAnalysis = analyzer.analyzeDetectionResults(partialResults);
      expect(partialAnalysis.completeness).toBe(0.25); // 1 out of 4 variables = 25%
    });

    it('应该基于检测方法调整置信度', () => {
      const highConfidenceResults = [
        createDetectionResult('Shopline', true, 0.95, 'direct-assignment'),
        createDetectionResult('__ENV__', true, 0.93, 'json-parse')
      ];

      const lowConfidenceResults = [
        createDetectionResult('Shopline', true, 0.80, 'function-call'),
        createDetectionResult('__ENV__', true, 0.75, 'object-property')
      ];

      const highAnalysis = analyzer.analyzeDetectionResults(highConfidenceResults);
      const lowAnalysis = analyzer.analyzeDetectionResults(lowConfidenceResults);

      expect(highAnalysis.confidence).toBeGreaterThan(lowAnalysis.confidence);
    });

    it('应该基于数据大小调整置信度', () => {
      const largeDataResults = [
        createDetectionResult('Shopline', true, 0.90, 'direct-assignment', 2000),
        createDetectionResult('__ENV__', true, 0.90, 'direct-assignment', 1500)
      ];

      const smallDataResults = [
        createDetectionResult('Shopline', true, 0.90, 'direct-assignment', 50),
        createDetectionResult('__ENV__', true, 0.90, 'direct-assignment', 30)
      ];

      const largeAnalysis = analyzer.analyzeDetectionResults(largeDataResults);
      const smallAnalysis = analyzer.analyzeDetectionResults(smallDataResults);

      expect(largeAnalysis.confidence).toBeGreaterThan(smallAnalysis.confidence);
    });
  });

  describe('recommendations', () => {
    it('应该为SHOPLINE 1.0提供升级建议', () => {
      const results = [
        createDetectionResult('mainConfig', true, 0.95),
        createDetectionResult('shopline', true, 0.90)
      ];

      const analysis = analyzer.analyzeDetectionResults(results);

      expect(analysis.recommendations).toContain(
        'SHOPLINE 1.0 detected - consider upgrading to 2.0 for enhanced features'
      );
    });

    it('应该为SHOPLINE 2.0提供确认信息', () => {
      const results = [
        createDetectionResult('Shopline', true, 0.95),
        createDetectionResult('__ENV__', true, 0.93)
      ];

      const analysis = analyzer.analyzeDetectionResults(results);

      expect(analysis.recommendations).toContain(
        'SHOPLINE 2.0 detected - modern platform with full feature support'
      );
    });

    it('应该为混合版本提供迁移建议', () => {
      const results = [
        createDetectionResult('mainConfig', true, 0.90),
        createDetectionResult('Shopline', true, 0.92)
      ];

      const analysis = analyzer.analyzeDetectionResults(results);

      expect(analysis.recommendations).toContain(
        'Mixed version detected - migration may be in progress'
      );
    });

    it('应该为完整集成提供建议', () => {
      const results = [
        createDetectionResult('Shopline', true, 0.95),
        createDetectionResult('__ENV__', true, 0.93)
      ];

      const analysis = analyzer.analyzeDetectionResults(results);

      expect(analysis.recommendations).toContain(
        'Comprehensive SHOPLINE integration detected'
      );
    });

    it('应该为低质量数据提供警告', () => {
      const lowQualityResults = [
        createDetectionResult('Shopline', true, 0.60, 'function-call', 10)
      ];

      const analysis = analyzer.analyzeDetectionResults(lowQualityResults);

      expect(analysis.recommendations.some(r => 
        r.includes('Low data completeness') || 
        r.includes('Low detection accuracy')
      )).toBe(true);
    });

    it('应该为非SHOPLINE网站提供明确信息', () => {
      const results = [
        createDetectionResult('mainConfig', false),
        createDetectionResult('Shopline', false)
      ];

      const analysis = analyzer.analyzeDetectionResults(results);

      expect(analysis.recommendations).toContain(
        'This does not appear to be a SHOPLINE store'
      );
    });
  });

  describe('generateDetailedReport', () => {
    it('应该生成格式化的详细报告', () => {
      const results = [
        createDetectionResult('Shopline', true, 0.95),
        createDetectionResult('__ENV__', true, 0.93)
      ];

      const analysis = analyzer.analyzeDetectionResults(results);
      const report = analyzer.generateDetailedReport(analysis);

      expect(report).toContain('SHOPLINE STORE ANALYSIS REPORT');
      expect(report).toContain('SHOPLINE 2.0 Store');
      expect(report).toContain('Found Variables:');
      expect(report).toContain('✓ Shopline');
      expect(report).toContain('✓ __ENV__');
      expect(report).toContain('Detection Methods:');
      expect(report).toContain('Platform Features:');
      expect(report).toContain('Recommendations:');
      expect(report).toContain('Analysis completed at:');
    });

    it('应该包含所有关键信息', () => {
      const results = [
        createDetectionResult('mainConfig', true, 0.90, 'direct-assignment', 500),
        createDetectionResult('shopline', true, 0.85, 'variable-declaration', 300)
      ];

      const analysis = analyzer.analyzeDetectionResults(results);
      const report = analyzer.generateDetailedReport(analysis);

      expect(report).toContain('Platform Version: 1.0');
      expect(report).toContain('direct-assignment');
      expect(report).toContain('variable-declaration');
      expect(report).toContain('legacy-api');
    });
  });
});
