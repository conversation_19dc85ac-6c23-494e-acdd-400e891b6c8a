/**
 * 增强静态分析 - HTML解析引擎
 * 开发script标签提取和内容解析功能，支持多种HTML结构
 */

export interface ScriptBlock {
  id: number;
  content: string;
  startPosition: number;
  length: number;
  hasInlineCode: boolean;
  type: 'inline' | 'external' | 'json' | 'module';
  src?: string;
  attributes: Record<string, string>;
}

export interface ParsedHtml {
  scriptBlocks: ScriptBlock[];
  totalScripts: number;
  inlineScripts: number;
  externalScripts: number;
  htmlSize: number;
  parseTime: number;
}

export interface HtmlParserOptions {
  extractExternalScripts?: boolean;
  minScriptLength?: number;
  maxScriptLength?: number;
  includeJsonScripts?: boolean;
}

export class HtmlParserError extends Error {
  constructor(message: string, public position?: number) {
    super(message);
    this.name = 'HtmlParserError';
  }
}

export class HtmlParser {
  private readonly defaultOptions: Required<HtmlParserOptions> = {
    extractExternalScripts: false,
    minScriptLength: 10,
    maxScriptLength: 1024 * 1024, // 1MB
    includeJsonScripts: true
  };

  constructor(private options: HtmlParserOptions = {}) {
    this.options = { ...this.defaultOptions, ...options };
  }

  /**
   * 解析HTML并提取script标签
   */
  parseHtml(html: string): ParsedHtml {
    const startTime = performance.now();

    try {
      this.validateHtml(html);
      
      const scriptBlocks = this.extractScriptBlocks(html);
      const endTime = performance.now();

      const result: ParsedHtml = {
        scriptBlocks,
        totalScripts: scriptBlocks.length,
        inlineScripts: scriptBlocks.filter(s => s.type === 'inline').length,
        externalScripts: scriptBlocks.filter(s => s.type === 'external').length,
        htmlSize: html.length,
        parseTime: Math.round(endTime - startTime)
      };

      console.log(`📄 HTML解析完成: ${result.totalScripts} 个脚本 (${result.inlineScripts} 内联, ${result.externalScripts} 外部) - ${result.parseTime}ms`);

      return result;

    } catch (error) {
      const endTime = performance.now();
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`❌ HTML解析失败: ${errorMessage} - ${Math.round(endTime - startTime)}ms`);

      if (error instanceof HtmlParserError) {
        throw error;
      }

      throw new HtmlParserError(`HTML parsing failed: ${errorMessage}`);
    }
  }

  /**
   * 提取所有script标签内容
   */
  private extractScriptBlocks(html: string): ScriptBlock[] {
    const scriptBlocks: ScriptBlock[] = [];
    
    // 匹配所有script标签，包括属性
    const scriptRegex = /<script(?:\s+([^>]*?))?>([\s\S]*?)<\/script>/gi;
    let match;
    let index = 0;

    while ((match = scriptRegex.exec(html)) !== null) {
      const [, attributesStr = '', content = ''] = match;
      const startPos = match.index;

      try {
        const attributes = this.parseAttributes(attributesStr);
        const scriptType = this.determineScriptType(content, attributes);

        // 根据选项过滤脚本
        if (!this.shouldIncludeScript(content, scriptType)) {
          continue;
        }

        const scriptBlock: ScriptBlock = {
          id: index++,
          content: content.trim(),
          startPosition: startPos,
          length: content.length,
          hasInlineCode: this.hasInlineCode(content, attributes),
          type: scriptType,
          ...(attributes['src'] && { src: attributes['src'] }),
          attributes
        };

        scriptBlocks.push(scriptBlock);

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`⚠️ 跳过无效脚本块 (位置 ${startPos}): ${errorMessage}`);
        continue;
      }
    }

    return scriptBlocks;
  }

  /**
   * 解析script标签的属性
   */
  private parseAttributes(attributesStr: string): Record<string, string> {
    const attributes: Record<string, string> = {};
    
    if (!attributesStr.trim()) {
      return attributes;
    }

    // 匹配属性：name="value" 或 name='value' 或 name=value 或 name
    const attrRegex = /(\w+)(?:\s*=\s*(?:"([^"]*)"|'([^']*)'|([^\s>]+)))?/g;
    let match;

    while ((match = attrRegex.exec(attributesStr)) !== null) {
      const [, name, doubleQuoted, singleQuoted, unquoted] = match;
      if (name) {
        const value = doubleQuoted || singleQuoted || unquoted || '';
        attributes[name.toLowerCase()] = value;
      }
    }

    return attributes;
  }

  /**
   * 确定脚本类型
   */
  private determineScriptType(content: string, attributes: Record<string, string>): ScriptBlock['type'] {
    // 外部脚本
    if (attributes['src']) {
      return 'external';
    }

    // 模块脚本
    if (attributes['type'] === 'module') {
      return 'module';
    }

    // JSON脚本
    if (attributes['type'] === 'application/json' ||
        attributes['type'] === 'application/ld+json' ||
        this.looksLikeJson(content)) {
      return 'json';
    }

    // 内联脚本
    return 'inline';
  }

  /**
   * 检查内容是否看起来像JSON
   */
  private looksLikeJson(content: string): boolean {
    const trimmed = content.trim();
    return (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
           (trimmed.startsWith('[') && trimmed.endsWith(']'));
  }

  /**
   * 检查是否包含实际的JavaScript代码
   */
  private hasInlineCode(content: string, attributes: Record<string, string>): boolean {
    // 外部脚本不算内联代码
    if (attributes['src']) {
      return false;
    }

    // JSON脚本不算内联代码
    if (attributes['type'] === 'application/json' ||
        attributes['type'] === 'application/ld+json') {
      return false;
    }

    const trimmed = content.trim();
    
    // 检查长度
    if (trimmed.length < (this.options?.minScriptLength ?? 10)) {
      return false;
    }

    // 检查是否包含JavaScript关键字或模式
    const jsPatterns = [
      /\bwindow\./,
      /\bvar\s+\w+/,
      /\blet\s+\w+/,
      /\bconst\s+\w+/,
      /\bfunction\s+\w+/,
      /\b(if|for|while|switch)\s*\(/,
      /\w+\s*=\s*\{/,
      /\w+\s*=\s*\[/,
      /\w+\s*=\s*function/,
      /\w+\s*=\s*\w+\(/
    ];

    return jsPatterns.some(pattern => pattern.test(trimmed));
  }

  /**
   * 根据选项判断是否应该包含此脚本
   */
  private shouldIncludeScript(content: string, type: ScriptBlock['type']): boolean {
    // 检查脚本类型
    if (type === 'external' && !this.options.extractExternalScripts) {
      return false;
    }

    if (type === 'json' && !this.options.includeJsonScripts) {
      return false;
    }

    // 外部脚本不检查内容长度
    if (type === 'external') {
      return true;
    }

    // 检查长度限制
    if (!content || content.length < (this.options?.minScriptLength ?? 10)) {
      return false;
    }

    if (content.length > (this.options?.maxScriptLength ?? 1000000)) {
      console.warn(`⚠️ 脚本过大，跳过: ${(content.length / 1024).toFixed(1)}KB`);
      return false;
    }

    return true;
  }

  /**
   * 验证HTML格式
   */
  private validateHtml(html: string): void {
    if (!html || typeof html !== 'string') {
      throw new HtmlParserError('Invalid HTML: content must be a non-empty string');
    }

    if (html.length === 0) {
      throw new HtmlParserError('Invalid HTML: content is empty');
    }

    if (html.length > 50 * 1024 * 1024) { // 50MB限制
      throw new HtmlParserError(`HTML too large: ${(html.length / 1024 / 1024).toFixed(1)}MB. Maximum allowed: 50MB.`);
    }

    // 基本HTML结构检查
    if (!html.includes('<') || !html.includes('>')) {
      throw new HtmlParserError('Invalid HTML: no HTML tags found');
    }
  }

  /**
   * 获取脚本内容的摘要信息
   */
  getScriptSummary(scriptBlock: ScriptBlock): {
    preview: string;
    lineCount: number;
    hasVariableAssignments: boolean;
    hasWindowAccess: boolean;
    estimatedComplexity: 'low' | 'medium' | 'high';
  } {
    const content = scriptBlock.content;
    const lines = content.split('\n');
    
    return {
      preview: content.substring(0, 200) + (content.length > 200 ? '...' : ''),
      lineCount: lines.length,
      hasVariableAssignments: /\w+\s*=\s*\{/.test(content),
      hasWindowAccess: /\bwindow\./.test(content),
      estimatedComplexity: this.estimateComplexity(content)
    };
  }

  /**
   * 估算脚本复杂度
   */
  private estimateComplexity(content: string): 'low' | 'medium' | 'high' {
    const complexityIndicators = [
      /\bfunction\s+\w+/g,
      /\bif\s*\(/g,
      /\bfor\s*\(/g,
      /\bwhile\s*\(/g,
      /\btry\s*\{/g,
      /\bcatch\s*\(/g,
      /\w+\s*=\s*\{[^}]{50,}/g // 大对象
    ];

    let score = 0;
    for (const pattern of complexityIndicators) {
      const matches = content.match(pattern);
      score += matches ? matches.length : 0;
    }

    if (score < 5) return 'low';
    if (score < 15) return 'medium';
    return 'high';
  }

  /**
   * 查找包含特定模式的脚本块
   */
  findScriptsWithPattern(scriptBlocks: ScriptBlock[], pattern: RegExp): ScriptBlock[] {
    return scriptBlocks.filter(block => 
      block.hasInlineCode && pattern.test(block.content)
    );
  }

  /**
   * 获取解析统计信息
   */
  getParsingStats(parsedHtml: ParsedHtml): {
    efficiency: number;
    avgScriptSize: number;
    largestScript: number;
    scriptDensity: number;
  } {
    const { scriptBlocks, htmlSize, parseTime } = parsedHtml;
    
    if (scriptBlocks.length === 0) {
      return {
        efficiency: 0,
        avgScriptSize: 0,
        largestScript: 0,
        scriptDensity: 0
      };
    }

    const scriptSizes = scriptBlocks.map(s => s.length);
    const totalScriptSize = scriptSizes.reduce((sum, size) => sum + size, 0);

    return {
      efficiency: Math.round(scriptBlocks.length / parseTime * 1000), // 脚本数/秒
      avgScriptSize: Math.round(totalScriptSize / scriptBlocks.length),
      largestScript: Math.max(...scriptSizes),
      scriptDensity: Math.round((totalScriptSize / htmlSize) * 100) // 脚本占HTML的百分比
    };
  }
}
