/**
 * 增强静态分析 - 主分析器
 * 整合HTTP客户端、HTML解析、变量检测和结果分析
 */

import { HttpClient, type HttpClientOptions } from './http-client';
import { HtmlParser, type HtmlParserOptions } from './html-parser';
import { VariableDetectionEngine } from './variable-detector';
import { ResultAnalysisEngine, type ShoplineAnalysis } from './result-analyzer';

export interface StaticAnalysisOptions {
  http?: HttpClientOptions;
  html?: HtmlParserOptions;
  enableDetailedReport?: boolean;
  enablePerformanceMetrics?: boolean;
}

export interface StaticAnalysisResult {
  success: boolean;
  method: 'static-analysis';
  url: string;
  timing: {
    total: number;
    httpFetch: number;
    htmlParsing: number;
    variableDetection: number;
    resultAnalysis: number;
  };
  analysis: ShoplineAnalysis;
  detailedReport?: string;
  performanceMetrics?: {
    httpMetrics: any;
    parsingStats: any;
    detectionStats: any;
  };
  error?: string;
}

export class StaticAnalysisError extends Error {
  constructor(
    message: string,
    public stage: 'http' | 'parsing' | 'detection' | 'analysis',
    public originalError?: Error
  ) {
    super(message);
    this.name = 'StaticAnalysisError';
  }
}

export class StaticAnalyzer {
  private httpClient: HttpClient;
  private htmlParser: HtmlParser;
  private variableDetector: VariableDetectionEngine;
  private resultAnalyzer: ResultAnalysisEngine;

  constructor(private options: StaticAnalysisOptions = {}) {
    this.httpClient = new HttpClient(options.http);
    this.htmlParser = new HtmlParser(options.html);
    this.variableDetector = new VariableDetectionEngine();
    this.resultAnalyzer = new ResultAnalysisEngine();
  }

  /**
   * 执行完整的静态分析
   */
  async analyze(url: string): Promise<StaticAnalysisResult> {
    const startTime = performance.now();
    const timing = {
      total: 0,
      httpFetch: 0,
      htmlParsing: 0,
      variableDetection: 0,
      resultAnalysis: 0
    };

    try {
      console.log(`🔍 开始静态分析: ${url}`);

      // 1. HTTP获取页面内容
      const httpStart = performance.now();
      const httpResponse = await this.httpClient.fetchPage(url);
      timing.httpFetch = Math.round(performance.now() - httpStart);

      console.log(`📄 页面获取完成: ${timing.httpFetch}ms, ${(httpResponse.size / 1024).toFixed(1)}KB`);

      // 2. HTML解析
      const parseStart = performance.now();
      const parsedHtml = this.htmlParser.parseHtml(httpResponse.html);
      timing.htmlParsing = Math.round(performance.now() - parseStart);

      console.log(`🔧 HTML解析完成: ${timing.htmlParsing}ms, ${parsedHtml.totalScripts} 个脚本`);

      // 3. 变量检测
      const detectionStart = performance.now();
      const detectionResults = await this.variableDetector.detectVariables(parsedHtml.scriptBlocks);
      timing.variableDetection = Math.round(performance.now() - detectionStart);

      const foundVariables = detectionResults.filter(r => r.found);
      console.log(`🎯 变量检测完成: ${timing.variableDetection}ms, 找到 ${foundVariables.length} 个变量`);

      // 4. 结果分析
      const analysisStart = performance.now();
      const analysis = this.resultAnalyzer.analyzeDetectionResults(detectionResults);
      timing.resultAnalysis = Math.round(performance.now() - analysisStart);

      timing.total = Math.round(performance.now() - startTime);

      console.log(`📊 结果分析完成: ${timing.resultAnalysis}ms`);
      console.log(`✅ 静态分析完成: ${timing.total}ms, 置信度 ${(analysis.confidence * 100).toFixed(1)}%`);

      // 构建结果
      const result: StaticAnalysisResult = {
        success: true,
        method: 'static-analysis',
        url,
        timing,
        analysis
      };

      // 可选的详细报告
      if (this.options.enableDetailedReport) {
        result.detailedReport = this.resultAnalyzer.generateDetailedReport(analysis);
      }

      // 可选的性能指标
      if (this.options.enablePerformanceMetrics) {
        result.performanceMetrics = {
          httpMetrics: this.httpClient.getMetrics(),
          parsingStats: this.htmlParser.getParsingStats(parsedHtml),
          detectionStats: this.variableDetector.getDetectionStats(detectionResults)
        };
      }

      return result;

    } catch (error) {
      timing.total = Math.round(performance.now() - startTime);
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`❌ 静态分析失败: ${errorMessage} (${timing.total}ms)`);

      // 确定错误阶段
      let stage: 'http' | 'parsing' | 'detection' | 'analysis' = 'http';
      if (timing.httpFetch > 0) stage = 'parsing';
      if (timing.htmlParsing > 0) stage = 'detection';
      if (timing.variableDetection > 0) stage = 'analysis';

      throw new StaticAnalysisError(
        `Static analysis failed at ${stage} stage: ${errorMessage}`,
        stage,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * 批量分析多个URL
   */
  async analyzeMultiple(urls: string[]): Promise<StaticAnalysisResult[]> {
    console.log(`🔍 开始批量静态分析: ${urls.length} 个URL`);
    
    const results: StaticAnalysisResult[] = [];
    
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      if (!url) {
        console.warn(`⚠️ 跳过无效URL: 索引 ${i}`);
        continue;
      }

      console.log(`📋 分析进度: ${i + 1}/${urls.length} - ${url}`);

      try {
        const result = await this.analyze(url);
        results.push(result);
      } catch (error) {
        // 单个URL失败不影响其他URL
        const errorMessage = error instanceof Error ? error.message : String(error);
        results.push({
          success: false,
          method: 'static-analysis',
          url,
          timing: { total: 0, httpFetch: 0, htmlParsing: 0, variableDetection: 0, resultAnalysis: 0 },
          analysis: {
            isShoplineStore: false,
            platformVersion: 'N/A',
            storeType: 'Analysis Failed',
            confidence: 0,
            foundVariables: [],
            totalVariablesFound: 0,
            totalDataSize: 0,
            completeness: 0,
            detectionMethods: [],
            analysisTimestamp: new Date().toISOString(),
            platformFeatures: [],
            recommendations: [`Analysis failed: ${errorMessage}`]
          },
          error: errorMessage
        });
      }
      
      // 添加延迟避免过快请求
      if (i < urls.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    console.log(`✅ 批量分析完成: ${successCount}/${urls.length} 成功`);
    
    return results;
  }

  /**
   * 获取分析器统计信息
   */
  getStats(): {
    httpStats: any;
    totalAnalyses: number;
    successRate: number;
  } {
    const httpMetrics = this.httpClient.getMetrics();
    
    return {
      httpStats: httpMetrics,
      totalAnalyses: httpMetrics.totalRequests,
      successRate: this.httpClient.getSuccessRate()
    };
  }

  /**
   * 重置分析器状态
   */
  reset(): void {
    this.httpClient.resetMetrics();
  }

  /**
   * 验证URL是否适合静态分析
   */
  validateUrl(url: string): { valid: boolean; reason?: string } {
    try {
      const parsedUrl = new URL(url);
      
      // 检查协议
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return { valid: false, reason: `Unsupported protocol: ${parsedUrl.protocol}` };
      }

      // 检查主机名
      if (!parsedUrl.hostname) {
        return { valid: false, reason: 'Invalid hostname' };
      }

      // 检查是否为私有IP (基础SSRF防护)
      const hostname = parsedUrl.hostname.toLowerCase();
      if (this.isPrivateIP(hostname)) {
        return { valid: false, reason: `Private IP addresses not allowed: ${hostname}` };
      }

      return { valid: true };

    } catch (error) {
      return { valid: false, reason: `Invalid URL format: ${url}` };
    }
  }

  /**
   * 检查是否为私有IP地址
   */
  private isPrivateIP(hostname: string): boolean {
    // 检查localhost
    if (['localhost', '127.0.0.1', '::1'].includes(hostname)) {
      return true;
    }

    // 检查私有IP范围
    const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    const match = hostname.match(ipv4Regex);
    
    if (match) {
      const [, a, b] = match.map(Number);

      // 私有IP范围
      return (
        (a === 10) ||
        (a === 172 && b !== undefined && b >= 16 && b <= 31) ||
        (a === 192 && b === 168) ||
        (a === 169 && b === 254) // 链路本地地址
      );
    }

    return false;
  }

  /**
   * 生成分析摘要
   */
  generateSummary(results: StaticAnalysisResult[]): {
    totalAnalyzed: number;
    successfulAnalyses: number;
    shoplineStores: number;
    platformVersions: Record<string, number>;
    avgConfidence: number;
    avgResponseTime: number;
    recommendations: string[];
  } {
    const successful = results.filter(r => r.success);
    const shoplineStores = successful.filter(r => r.analysis.isShoplineStore);
    
    const platformVersions: Record<string, number> = {};
    shoplineStores.forEach(r => {
      const version = r.analysis.platformVersion;
      platformVersions[version] = (platformVersions[version] || 0) + 1;
    });

    const avgConfidence = shoplineStores.length > 0
      ? shoplineStores.reduce((sum, r) => sum + r.analysis.confidence, 0) / shoplineStores.length
      : 0;

    const avgResponseTime = successful.length > 0
      ? successful.reduce((sum, r) => sum + r.timing.total, 0) / successful.length
      : 0;

    // 收集所有建议
    const allRecommendations = successful.flatMap(r => r.analysis.recommendations);
    const uniqueRecommendations = [...new Set(allRecommendations)];

    return {
      totalAnalyzed: results.length,
      successfulAnalyses: successful.length,
      shoplineStores: shoplineStores.length,
      platformVersions,
      avgConfidence: Math.round(avgConfidence * 100) / 100,
      avgResponseTime: Math.round(avgResponseTime),
      recommendations: uniqueRecommendations
    };
  }
}
