/**
 * 静态分析系统综合测试
 *
 * 测试覆盖范围：
 * - 端到端分析流程集成测试
 * - 真实SHOPLINE网站结构分析
 * - 复杂场景和边界条件
 * - 性能指标和详细报告生成
 * - 多平台版本混合检测
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { StaticAnalyzer } from './static-analyzer';

// Mock fetch for integration testing
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('Static Analysis System Integration', () => {
  let analyzer: StaticAnalyzer;

  beforeEach(() => {
    analyzer = new StaticAnalyzer({
      enableDetailedReport: true,
      enablePerformanceMetrics: true
    });
    vi.clearAllMocks();
  });

  /**
   * 创建模拟的HTTP响应
   * @param html - HTML内容
   * @param status - HTTP状态码
   * @returns 模拟的Response对象
   */
  const createMockResponse = (html: string, status = 200) => ({
    ok: status >= 200 && status < 300,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    headers: new Map([['content-type', 'text/html; charset=utf-8']]),
    text: () => Promise.resolve(html)
  });

  describe('End-to-End Analysis', () => {
    it('应该完整分析真实的SHOPLINE 1.0网站结构', async () => {
      const realShopline1Html = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="UTF-8">
            <title>Test Store</title>
            <script>
              window.mainConfig = {
                "merchantId": "603dc1ad7bc9170010fba5bc",
                "storeId": "store_12345",
                "storeName": "Test Store",
                "currency": "USD",
                "theme": "default",
                "features": ["cart", "checkout", "analytics"],
                "api": {
                  "baseUrl": "https://api.shoplineapp.com",
                  "version": "v1"
                }
              };
              
              window.shopline = {
                "version": "1.0.5",
                "build": "20231201",
                "modules": ["core", "cart", "checkout"],
                "config": window.mainConfig
              };
            </script>
          </head>
          <body>
            <div id="app">SHOPLINE Store Content</div>
          </body>
        </html>
      `;

      mockFetch.mockResolvedValueOnce(createMockResponse(realShopline1Html));

      const result = await analyzer.analyze('https://demo-store.shoplineapp.com');

      // 验证基本结果
      expect(result.success).toBe(true);
      expect(result.method).toBe('static-analysis');
      expect(result.url).toBe('https://demo-store.shoplineapp.com');

      // 验证分析结果
      expect(result.analysis.isShoplineStore).toBe(true);
      expect(result.analysis.platformVersion).toBe('1.0');
      expect(result.analysis.storeType).toBe('SHOPLINE 1.0 Store');
      expect(result.analysis.confidence).toBeGreaterThan(0.9);

      // 验证找到的变量
      expect(result.analysis.foundVariables).toContain('mainConfig');
      expect(result.analysis.foundVariables).toContain('shopline');
      expect(result.analysis.totalVariablesFound).toBe(2);

      // 验证平台特性
      expect(result.analysis.platformFeatures).toContain('legacy-api');
      expect(result.analysis.platformFeatures).toContain('basic-checkout');

      // 验证建议
      expect(result.analysis.recommendations).toContain(
        'SHOPLINE 1.0 detected - consider upgrading to 2.0 for enhanced features'
      );

      // 验证性能指标
      expect(result.timing.total).toBeGreaterThan(0);
      expect(result.performanceMetrics).toBeDefined();
      expect(result.detailedReport).toContain('SHOPLINE STORE ANALYSIS REPORT');
    });

    it('应该完整分析真实的SHOPLINE 2.0网站结构', async () => {
      const realShopline2Html = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="UTF-8">
            <title>Modern Store</title>
            <script>
              window.__ENV__ = {
                "APP_ENV": "production",
                "API_VERSION": "v2",
                "STORE_ID": "store_67890",
                "MERCHANT_ID": "merchant_abc123",
                "FEATURES": {
                  "checkout": true,
                  "analytics": true,
                  "multiCurrency": true,
                  "advancedShipping": true
                }
              };
              
              window.Shopline = {
                "store": {
                  "id": "store_67890",
                  "name": "Modern Store",
                  "domain": "modern-store.myshopline.com",
                  "currency": "USD",
                  "locale": "en-US"
                },
                "config": {
                  "api": {
                    "baseUrl": "https://api.myshopline.com",
                    "version": "v2",
                    "timeout": 30000
                  },
                  "features": window.__ENV__.FEATURES,
                  "theme": {
                    "name": "modern",
                    "version": "2.1.0"
                  }
                },
                "version": "2.0.8",
                "build": "20240101"
              };
            </script>
          </head>
          <body>
            <div id="shopline-app">SHOPLINE 2.0 Store</div>
          </body>
        </html>
      `;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([
          ['content-type', 'text/html; charset=utf-8'],
          ['content-length', realShopline2Html.length.toString()]
        ]),
        text: () => Promise.resolve(realShopline2Html)
      });

      const result = await analyzer.analyze('https://modern-store.myshopline.com');

      // 验证基本结果
      expect(result.success).toBe(true);
      expect(result.analysis.isShoplineStore).toBe(true);
      expect(result.analysis.platformVersion).toBe('2.0');
      expect(result.analysis.storeType).toBe('SHOPLINE 2.0 Store');
      expect(result.analysis.confidence).toBeGreaterThan(0.9);

      // 验证找到的变量
      expect(result.analysis.foundVariables).toContain('Shopline');
      expect(result.analysis.foundVariables).toContain('__ENV__');
      expect(result.analysis.totalVariablesFound).toBe(2);

      // 验证平台特性
      expect(result.analysis.platformFeatures).toContain('modern-api');
      expect(result.analysis.platformFeatures).toContain('enhanced-analytics');
      expect(result.analysis.platformFeatures).toContain('multi-currency');

      // 验证建议
      expect(result.analysis.recommendations).toContain(
        'SHOPLINE 2.0 detected - modern platform with full feature support'
      );
      expect(result.analysis.recommendations).toContain(
        'Comprehensive SHOPLINE integration detected'
      );

      // 验证数据完整性
      expect(result.analysis.completeness).toBe(1.0); // 100% completeness for 2.0
    });

    it('应该识别混合版本的SHOPLINE网站', async () => {
      const mixedVersionHtml = `
        <html>
          <head>
            <script>
              // Legacy 1.0 variables
              window.mainConfig = {
                "merchantId": "legacy_merchant",
                "storeId": "legacy_store"
              };
              
              // Modern 2.0 variables
              window.Shopline = {
                "store": {"id": "new_store"},
                "migration": {"inProgress": true, "from": "1.0", "to": "2.0"}
              };
              
              window.__ENV__ = {
                "APP_ENV": "migration",
                "MIGRATION_MODE": true
              };
            </script>
          </head>
          <body>Migration in progress</body>
        </html>
      `;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(mixedVersionHtml)
      });

      const result = await analyzer.analyze('https://migrating-store.shoplineapp.com');

      expect(result.success).toBe(true);
      expect(result.analysis.isShoplineStore).toBe(true);
      expect(result.analysis.platformVersion).toBe('Mixed');
      expect(result.analysis.storeType).toBe('SHOPLINE Mixed Version Store');
      expect(result.analysis.platformFeatures).toContain('hybrid-setup');
      expect(result.analysis.platformFeatures).toContain('migration-in-progress');
      expect(result.analysis.recommendations).toContain(
        'Mixed version detected - migration may be in progress'
      );
    });

    it('应该处理复杂的JavaScript对象和嵌套结构', async () => {
      const complexHtml = `
        <html>
          <head>
            <script>
              window.Shopline = {
                store: {
                  id: "complex_store",
                  settings: {
                    theme: {
                      name: "advanced",
                      customizations: {
                        colors: {
                          primary: "#ff6b6b",
                          secondary: "#4ecdc4"
                        },
                        fonts: ["Roboto", "Open Sans"],
                        layout: {
                          header: "sticky",
                          sidebar: "collapsible"
                        }
                      }
                    },
                    features: {
                      cart: {
                        enabled: true,
                        type: "drawer",
                        animations: true
                      },
                      checkout: {
                        enabled: true,
                        steps: ["shipping", "payment", "review"],
                        guestCheckout: true
                      }
                    }
                  }
                },
                api: {
                  endpoints: {
                    products: "/api/v2/products",
                    cart: "/api/v2/cart",
                    checkout: "/api/v2/checkout"
                  },
                  auth: {
                    type: "bearer",
                    refreshToken: true
                  }
                }
              };
            </script>
          </head>
          <body>Complex SHOPLINE store</body>
        </html>
      `;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(complexHtml)
      });

      const result = await analyzer.analyze('https://complex-store.myshopline.com');

      expect(result.success).toBe(true);
      expect(result.analysis.isShoplineStore).toBe(true);
      expect(result.analysis.foundVariables).toContain('Shopline');
      expect(result.analysis.totalDataSize).toBeGreaterThan(300); // Complex object
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('应该处理空的HTML页面', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve('<html><head></head><body></body></html>')
      });

      const result = await analyzer.analyze('https://empty-page.com');

      expect(result.success).toBe(true);
      expect(result.analysis.isShoplineStore).toBe(false);
      expect(result.analysis.foundVariables).toHaveLength(0);
    });

    it('应该处理格式错误的JavaScript', async () => {
      const malformedHtml = `
        <html>
          <head>
            <script>
              window.mainConfig = {
                "merchantId": "test",
                "invalidJson": {unclosed object
              };
            </script>
          </head>
          <body>Malformed JS</body>
        </html>
      `;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(malformedHtml)
      });

      const result = await analyzer.analyze('https://malformed-js.com');

      expect(result.success).toBe(true);
      // 应该仍然能检测到部分变量，即使有格式错误
      expect(result.analysis.foundVariables.length).toBeGreaterThanOrEqual(0);
    });

    it('应该处理非常大的HTML页面', async () => {
      const largeScript = 'x'.repeat(100000); // 100KB of data
      const largeHtml = `
        <html>
          <head>
            <script>
              window.Shopline = {"data": "${largeScript}"};
            </script>
          </head>
          <body>Large page</body>
        </html>
      `;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(largeHtml)
      });

      const result = await analyzer.analyze('https://large-page.com');

      expect(result.success).toBe(true);
      expect(result.analysis.totalDataSize).toBeGreaterThan(50000);
    });
  });

  describe('Performance Benchmarks', () => {
    it('应该在合理时间内完成分析', async () => {
      const normalHtml = `
        <html>
          <head>
            <script>
              window.Shopline = {"store": {"id": "perf_test"}};
              window.__ENV__ = {"APP_ENV": "test"};
            </script>
          </head>
          <body>Performance test</body>
        </html>
      `;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(normalHtml)
      });

      const startTime = performance.now();
      const result = await analyzer.analyze('https://perf-test.com');
      const endTime = performance.now();

      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
      expect(result.timing.total).toBeLessThan(5000);
    });
  });
});
