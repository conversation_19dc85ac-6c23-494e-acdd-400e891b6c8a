/**
 * 增强静态分析 - 变量检测引擎
 * 实现多策略模式匹配，检测SHOPLINE核心变量定义（mainConfig、shopline、Shopline、__ENV__）
 */

import type { ScriptBlock } from './html-parser';

export interface VariableConfig {
  name: string;
  patterns: string[];
  priority: number;
  platform: 'shopline-1.0' | 'shopline-2.0' | 'common';
}

export interface VariableDetectionResult {
  name: string;
  found: boolean;
  detectionMethod: string;
  scriptBlockId?: number;
  confidence: number;
  extractedValue?: any;
  size?: number;
  position?: {
    start: number;
    end: number;
  };
}

export interface DetectionStrategy {
  name: string;
  priority: number;
  detect(varName: string, content: string): DetectionResult;
}

export interface DetectionResult {
  found: boolean;
  method: string;
  confidence: number;
  extractedValue?: any;
  size?: number;
  position?: {
    start: number;
    end: number;
  };
}

export class VariableDetectionEngine {
  private readonly TARGET_VARIABLES: VariableConfig[] = [
    {
      name: 'mainConfig',
      patterns: ['window.mainConfig', 'mainConfig'],
      priority: 10,
      platform: 'shopline-1.0'
    },
    {
      name: 'shopline',
      patterns: ['window.shopline', 'shopline'],
      priority: 9,
      platform: 'shopline-1.0'
    },
    {
      name: 'Shopline',
      patterns: ['window.Shopline', 'Shopline'],
      priority: 10,
      platform: 'shopline-2.0'
    },
    {
      name: '__ENV__',
      patterns: ['window.__ENV__', '__ENV__'],
      priority: 10,
      platform: 'shopline-2.0'
    }
  ];

  private readonly detectionStrategies: DetectionStrategy[] = [
    {
      name: 'direct-assignment',
      priority: 10,
      detect: this.detectDirectAssignment.bind(this)
    },
    {
      name: 'variable-declaration',
      priority: 9,
      detect: this.detectVariableDeclaration.bind(this)
    },
    {
      name: 'json-parse',
      priority: 8,
      detect: this.detectJsonParse.bind(this)
    },
    {
      name: 'function-call',
      priority: 6,
      detect: this.detectFunctionCall.bind(this)
    },
    {
      name: 'object-property',
      priority: 5,
      detect: this.detectObjectProperty.bind(this)
    }
  ];

  /**
   * 检测脚本块中的所有目标变量
   */
  async detectVariables(scriptBlocks: ScriptBlock[]): Promise<VariableDetectionResult[]> {
    const results: VariableDetectionResult[] = [];

    for (const variable of this.TARGET_VARIABLES) {
      const detection = await this.detectSingleVariable(variable, scriptBlocks);
      results.push(detection);
    }

    // 按优先级和置信度排序
    return results.sort((a, b) => {
      if (a.found !== b.found) return a.found ? -1 : 1;
      return b.confidence - a.confidence;
    });
  }

  /**
   * 检测单个变量
   */
  private async detectSingleVariable(
    variable: VariableConfig,
    scriptBlocks: ScriptBlock[]
  ): Promise<VariableDetectionResult> {
    
    for (const block of scriptBlocks) {
      if (!block.hasInlineCode) continue;

      // 按优先级尝试不同的检测策略
      for (const strategy of this.detectionStrategies) {
        const result = strategy.detect(variable.name, block.content);
        
        if (result.found) {
          return {
            name: variable.name,
            found: true,
            detectionMethod: result.method,
            scriptBlockId: block.id,
            confidence: this.calculateConfidence(result, variable, block),
            extractedValue: result.extractedValue,
            size: result.size || 0,
            ...(result.position && { position: result.position })
          };
        }
      }
    }

    return {
      name: variable.name,
      found: false,
      detectionMethod: 'none',
      confidence: 0
    };
  }

  /**
   * 检测直接赋值模式: window.varName = {...}
   */
  private detectDirectAssignment(varName: string, content: string): DetectionResult {
    const patterns = [
      new RegExp(`window\\.${varName}\\s*=\\s*(\\{[\\s\\S]*?\\});?`, 'g'),
      new RegExp(`window\\["${varName}"\\]\\s*=\\s*(\\{[\\s\\S]*?\\});?`, 'g'),
      new RegExp(`window\\['${varName}'\\]\\s*=\\s*(\\{[\\s\\S]*?\\});?`, 'g')
    ];

    for (const pattern of patterns) {
      pattern.lastIndex = 0; // 重置正则表达式状态
      const match = pattern.exec(content);
      if (match && match[1]) {
        const objectStr = match[1];
        const extractedValue = this.extractObjectValue(objectStr);
        return {
          found: true,
          method: 'direct-assignment',
          confidence: 0.95,
          extractedValue,
          size: objectStr.length,
          position: {
            start: match.index || 0,
            end: (match.index || 0) + match[0].length
          }
        };
      }
    }

    return { found: false, method: 'direct-assignment', confidence: 0 };
  }

  /**
   * 检测变量声明模式: var config = {...}; window.varName = config;
   */
  private detectVariableDeclaration(varName: string, content: string): DetectionResult {
    // 查找变量声明后赋值给window的模式
    const declarationPattern = new RegExp(
      `(var|let|const)\\s+(\\w+)\\s*=\\s*(\\{[\\s\\S]*?\\});?[\\s\\S]*?window\\.${varName}\\s*=\\s*\\2`,
      'g'
    );

    declarationPattern.lastIndex = 0;
    const match = declarationPattern.exec(content);
    if (match && match[3]) {
      const objectStr = match[3];
      const extractedValue = this.extractObjectValue(objectStr);
      return {
        found: true,
        method: 'variable-declaration',
        confidence: 0.90,
        extractedValue,
        size: objectStr.length,
        position: {
          start: match.index || 0,
          end: (match.index || 0) + match[0].length
        }
      };
    }

    return { found: false, method: 'variable-declaration', confidence: 0 };
  }

  /**
   * 检测JSON解析模式: window.varName = JSON.parse(...)
   */
  private detectJsonParse(varName: string, content: string): DetectionResult {
    const pattern = new RegExp(
      `window\\.${varName}\\s*=\\s*JSON\\.parse\\s*\\(\\s*['"]([^'"]*?)['"]\\s*\\)`,
      'g'
    );

    pattern.lastIndex = 0;
    const match = pattern.exec(content);
    if (match && match[1]) {
      try {
        const jsonStr = match[1].replace(/\\"/g, '"').replace(/\\'/g, "'");
        const jsonValue = JSON.parse(jsonStr);
        return {
          found: true,
          method: 'json-parse',
          confidence: 0.95,
          extractedValue: jsonValue,
          size: jsonStr.length,
          position: {
            start: match.index || 0,
            end: (match.index || 0) + match[0].length
          }
        };
      } catch (e) {
        return {
          found: true,
          method: 'json-parse',
          confidence: 0.70, // JSON解析失败但模式匹配
          extractedValue: match[1],
          size: match[1].length,
          position: {
            start: match.index || 0,
            end: (match.index || 0) + match[0].length
          }
        };
      }
    }

    return { found: false, method: 'json-parse', confidence: 0 };
  }

  /**
   * 检测函数调用模式: window.varName = someFunction()
   */
  private detectFunctionCall(varName: string, content: string): DetectionResult {
    const pattern = new RegExp(
      `window\\.${varName}\\s*=\\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*\\([^)]*\\)`,
      'g'
    );

    pattern.lastIndex = 0;
    const match = pattern.exec(content);
    if (match) {
      return {
        found: true,
        method: 'function-call',
        confidence: 0.80, // 较低置信度，因为需要执行才能确定
        extractedValue: null, // 无法静态提取值
        size: 0,
        position: {
          start: match.index,
          end: match.index + match[0].length
        }
      };
    }

    return { found: false, method: 'function-call', confidence: 0 };
  }

  /**
   * 检测对象属性模式: {varName: {...}}
   */
  private detectObjectProperty(varName: string, content: string): DetectionResult {
    const patterns = [
      new RegExp(`["']${varName}["']\\s*:\\s*(\\{[\\s\\S]*?\\})`, 'g'),
      new RegExp(`\\b${varName}\\s*:\\s*(\\{[\\s\\S]*?\\})`, 'g')
    ];

    for (const pattern of patterns) {
      pattern.lastIndex = 0;
      const match = pattern.exec(content);
      if (match && match[1]) {
        const objectStr = match[1];
        const extractedValue = this.extractObjectValue(objectStr);
        return {
          found: true,
          method: 'object-property',
          confidence: 0.75,
          extractedValue,
          size: objectStr.length,
          position: {
            start: match.index || 0,
            end: (match.index || 0) + match[0].length
          }
        };
      }
    }

    return { found: false, method: 'object-property', confidence: 0 };
  }

  /**
   * 提取对象值的辅助方法
   */
  private extractObjectValue(objectStr: string): any {
    if (!objectStr) return null;

    try {
      // 尝试直接解析JSON
      return JSON.parse(objectStr);
    } catch (e) {
      // JSON解析失败，尝试清理后再解析
      try {
        const cleanedStr = this.cleanObjectString(objectStr);
        return JSON.parse(cleanedStr);
      } catch (e2) {
        // 仍然失败，返回原始字符串
        return objectStr;
      }
    }
  }

  /**
   * 清理对象字符串，使其符合JSON格式
   */
  private cleanObjectString(str: string): string {
    let cleaned = str;

    // 移除注释
    cleaned = cleaned.replace(/\/\*[\s\S]*?\*\//g, '');
    cleaned = cleaned.replace(/\/\/.*$/gm, '');

    // 处理单引号字符串
    cleaned = cleaned.replace(/'([^']*)'/g, '"$1"');

    // 添加引号到未引用的属性名
    cleaned = cleaned.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');

    // 添加引号到未引用的字符串值（但不包括数字、布尔值、null）
    cleaned = cleaned.replace(/:\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*([,}\]])/g, (match, value, suffix) => {
      // 检查是否是保留字
      if (['true', 'false', 'null', 'undefined'].includes(value)) {
        return match.replace('undefined', 'null'); // 将undefined转换为null
      }
      return `:"${value}"${suffix}`;
    });

    // 移除尾随逗号
    cleaned = cleaned.replace(/,\s*([}\]])/g, '$1');

    return cleaned;
  }

  /**
   * 计算检测置信度
   */
  private calculateConfidence(
    result: DetectionResult,
    variable: VariableConfig,
    block: ScriptBlock
  ): number {
    let confidence = result.confidence;

    // 基于变量优先级调整
    confidence += (variable.priority / 10) * 0.1;

    // 基于提取值的大小调整
    if (result.size && result.size > 100) {
      confidence += 0.05;
    }

    // 基于脚本块类型调整
    if (block.type === 'inline') {
      confidence += 0.05;
    }

    // 确保置信度在0-1范围内
    return Math.min(Math.max(confidence, 0), 1);
  }

  /**
   * 获取检测统计信息
   */
  getDetectionStats(results: VariableDetectionResult[]): {
    totalVariables: number;
    foundVariables: number;
    avgConfidence: number;
    detectionMethods: Record<string, number>;
    platformDetection: {
      'shopline-1.0': number;
      'shopline-2.0': number;
      'common': number;
    };
  } {
    const foundResults = results.filter(r => r.found);
    
    const detectionMethods: Record<string, number> = {};
    foundResults.forEach(r => {
      detectionMethods[r.detectionMethod] = (detectionMethods[r.detectionMethod] || 0) + 1;
    });

    const platformDetection = {
      'shopline-1.0': 0,
      'shopline-2.0': 0,
      'common': 0
    };

    foundResults.forEach(r => {
      const variable = this.TARGET_VARIABLES.find(v => v.name === r.name);
      if (variable) {
        platformDetection[variable.platform]++;
      }
    });

    return {
      totalVariables: results.length,
      foundVariables: foundResults.length,
      avgConfidence: foundResults.length > 0 
        ? foundResults.reduce((sum, r) => sum + r.confidence, 0) / foundResults.length 
        : 0,
      detectionMethods,
      platformDetection
    };
  }
}
