# 双重检测模式使用指南

## 📖 概述

SHOPLINE Checker 现在支持多种检测模式，为不同的使用场景提供最优的检测方案。本指南将帮助您了解如何选择和使用最适合的检测模式。

## 🎯 检测模式对比

| 检测模式 | 速度 | 准确性 | 资源消耗 | 适用场景 |
|---------|------|--------|----------|----------|
| **静态分析** | ⚡⚡⚡ 快速 (1-3秒) | 🎯🎯 中等 (90-95%) | 💾 低 (10MB) | 批量分析、快速检测 |
| **动态检测** | ⚡ 较慢 (3-10秒) | 🎯🎯🎯 高 (95-99%) | 💾💾💾 高 (200MB+) | 复杂交互、精确检测 |
| **混合模式** | ⚡⚡ 中等 (3-8秒) | 🎯🎯🎯 最高 (98-99%) | 💾💾 中等 (100MB) | 关键业务、全面分析 |
| **智能选择** | ⚡⚡ 自适应 | 🎯🎯🎯 自适应 | 💾💾 自适应 | 通用场景、自动优化 |

## 🚀 快速开始

### 1. 基础静态分析

最快速的检测方式，适合批量处理：

```bash
curl -X POST http://localhost:3001/analyze/static \
  -H "Content-Type: application/json" \
  -d '{"url": "https://demo-store.shoplineapp.com"}'
```

**优势：**
- 速度快 (1-3秒)
- 资源消耗低
- 支持批量处理

**适用场景：**
- 大规模网站分析
- 快速筛选
- 资源受限环境

### 2. 智能增强检测

推荐的默认选择，自动选择最佳模式：

```bash
curl -X POST http://localhost:3001/detect/enhanced \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://demo-store.shoplineapp.com",
    "options": {
      "mode": "auto"
    }
  }'
```

**智能选择逻辑：**
- 已知 SHOPLINE 域名 → 静态分析
- 复杂交互页面 → 动态检测
- 高准确性要求 → 混合模式

### 3. 混合检测模式

最全面的检测方式，同时运行两种检测：

```bash
curl -X POST http://localhost:3001/detect/enhanced \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://demo-store.shoplineapp.com",
    "options": {
      "mode": "hybrid",
      "enableComparison": true
    }
  }'
```

**混合模式特性：**
- 并行执行静态分析和动态检测
- 结果对比和一致性验证
- 提供详细的差异分析
- 推荐最可信的结果

## 📊 最佳实践

### 场景1: 批量网站分析

**需求：** 分析1000+个网站，快速筛选SHOPLINE网站

**推荐方案：** 批量静态分析

```bash
curl -X POST http://localhost:3001/analyze/static/batch \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "https://store1.shoplineapp.com",
      "https://store2.myshopline.com",
      "https://store3.shoplineapp.com"
    ],
    "options": {
      "enablePerformanceMetrics": true
    }
  }'
```

**优势：**
- 处理速度：1000个网站约30-60分钟
- 资源消耗：相比动态检测节省95%资源
- 准确率：90-95%，足够筛选使用

### 场景2: 关键业务验证

**需求：** 验证重要客户网站的SHOPLINE配置

**推荐方案：** 混合检测模式

```bash
curl -X POST http://localhost:3001/detect/enhanced \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://important-client.shoplineapp.com",
    "options": {
      "mode": "hybrid",
      "enableComparison": true,
      "confidenceThreshold": 0.95
    }
  }'
```

**优势：**
- 最高准确率：98-99%
- 结果验证：两种方法交叉验证
- 详细报告：包含差异分析和建议

### 场景3: 实时监控

**需求：** 监控网站配置变化，及时发现问题

**推荐方案：** 智能选择模式

```bash
curl -X POST http://localhost:3001/detect/enhanced \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://monitored-store.shoplineapp.com",
    "options": {
      "mode": "auto",
      "staticOptions": {
        "enablePerformanceMetrics": true
      }
    }
  }'
```

**优势：**
- 自适应性能：根据网站特征选择最佳方式
- 平衡效率：在速度和准确性间找到最佳平衡
- 缓存优化：重复检测时利用缓存加速

### 场景4: 开发调试

**需求：** 开发过程中验证SHOPLINE变量配置

**推荐方案：** 动态检测模式

```bash
curl -X POST http://localhost:3001/detect/enhanced \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://dev-store.shoplineapp.com",
    "options": {
      "mode": "dynamic",
      "timeout": 30000,
      "waitTime": 3000
    }
  }'
```

**优势：**
- 最高准确性：能检测到动态加载的变量
- 实时执行：反映页面的真实运行状态
- 调试友好：提供详细的执行信息

## ⚙️ 高级配置

### 静态分析选项

```javascript
{
  "options": {
    "mode": "static",
    "staticOptions": {
      "http": {
        "timeout": 30000,
        "maxRetries": 3,
        "userAgent": "SHOPLINE-Checker/1.9.0"
      },
      "html": {
        "extractExternalScripts": false,
        "minScriptLength": 100,
        "maxScriptLength": 1048576
      },
      "enableDetailedReport": true,
      "enablePerformanceMetrics": true
    }
  }
}
```

### 混合检测选项

```javascript
{
  "options": {
    "mode": "hybrid",
    "enableComparison": true,
    "confidenceThreshold": 0.8,
    "staticOptions": {
      "enablePerformanceMetrics": true
    },
    "timeout": 45000,
    "waitTime": 2000
  }
}
```

### 智能选择选项

```javascript
{
  "options": {
    "mode": "auto",
    "confidenceThreshold": 0.85,
    "staticOptions": {
      "enablePerformanceMetrics": true
    }
  }
}
```

## 📈 性能优化建议

### 1. 缓存策略

- **静态分析结果**：缓存5分钟，适合内容相对稳定的网站
- **动态检测结果**：缓存3分钟，适合动态内容较多的网站
- **混合检测结果**：缓存10分钟，适合重要且稳定的网站

### 2. 批量处理优化

```bash
# 分批处理大量URL
for i in {1..10}; do
  curl -X POST http://localhost:3001/analyze/static/batch \
    -H "Content-Type: application/json" \
    -d "{\"urls\": $(cat urls_batch_$i.json)}"
  sleep 2  # 避免过载
done
```

### 3. 并发控制

- **静态分析**：支持高并发 (50+ 并发请求)
- **动态检测**：建议限制并发 (5-10 并发请求)
- **混合模式**：建议中等并发 (10-20 并发请求)

## 🔍 结果解读

### 静态分析结果

```json
{
  "success": true,
  "method": "static-analysis",
  "url": "https://demo-store.shoplineapp.com",
  "timing": {
    "total": 1250,
    "detection": 800,
    "analysis": 450,
    "breakdown": {
      "httpFetch": 200,
      "htmlParsing": 150,
      "variableDetection": 450,
      "resultAnalysis": 450
    }
  },
  "analysis": {
    "platformVersion": "SHOPLINE 2.0",
    "storeType": "Standard Store",
    "confidence": 0.92,
    "detectionMethods": ["direct-assignment", "json-parse"],
    "recommendations": ["Consider upgrading to latest SDK version"]
  }
}
```

### 混合检测结果

```json
{
  "success": true,
  "method": "hybrid",
  "comparison": {
    "agreement": {
      "variableAgreement": 0.95,
      "confidenceAgreement": 0.88,
      "overallAgreement": 0.92
    },
    "discrepancies": [],
    "recommendedResult": "merged"
  }
}
```

## 🚨 故障排除

### 常见问题

1. **静态分析失败**
   - 检查网站是否可访问
   - 验证URL格式是否正确
   - 检查网络连接

2. **动态检测超时**
   - 增加timeout设置
   - 检查浏览器资源是否充足
   - 验证网站加载速度

3. **混合模式结果不一致**
   - 查看comparison字段的详细信息
   - 检查discrepancies数组
   - 根据recommendedResult选择最佳结果

### 调试技巧

```bash
# 启用详细日志
export LOG_LEVEL=debug

# 查看性能指标
curl http://localhost:3001/stats

# 健康检查
curl http://localhost:3001/health
```

## 📚 更多资源

- [静态分析模块详解](./README.md)
- [API文档](../../README.md)
- [集成测试示例](../tempfiles/test-static-analysis-integration.cjs)
- [性能基准测试](../tempfiles/performance-benchmark.cjs)

---

*最后更新: 2025-01-04*  
*版本: v1.9.0*
