/**
 * 增强静态分析 - HTTP客户端模块
 * 实现优化的HTTP客户端，支持超时控制、错误处理和性能监控
 */

export interface HttpClientOptions {
  timeout?: number;
  maxRetries?: number;
  retryDelay?: number;
  userAgent?: string;
  followRedirects?: boolean;
}

export interface HttpResponse {
  html: string;
  size: number;
  timing: number;
  statusCode: number;
  headers: Record<string, string>;
  redirectCount: number;
}

export interface HttpMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  avgResponseTime: number;
  totalDataTransferred: number;
}

export class HttpClientError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public isTimeout?: boolean,
    public isNetworkError?: boolean
  ) {
    super(message);
    this.name = 'HttpClientError';
  }
}

export class HttpClient {
  private metrics: HttpMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    avgResponseTime: 0,
    totalDataTransferred: 0
  };

  private readonly defaultOptions: Required<HttpClientOptions> = {
    timeout: 15000, // 15秒超时
    maxRetries: 2,
    retryDelay: 1000,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    followRedirects: true
  };

  private readonly defaultHeaders = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate, br',
    'DNT': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none'
  };

  constructor(private options: HttpClientOptions = {}) {
    this.options = { ...this.defaultOptions, ...options };
  }

  /**
   * 获取页面内容
   */
  async fetchPage(url: string, customOptions?: Partial<HttpClientOptions>): Promise<HttpResponse> {
    const startTime = performance.now();
    const finalOptions: Required<HttpClientOptions> = {
      ...this.defaultOptions,
      ...this.options,
      ...customOptions
    };
    
    this.metrics.totalRequests++;

    try {
      this.validateUrl(url);
      
      const response = await this.fetchWithRetry(url, finalOptions);
      const endTime = performance.now();
      const timing = Math.round(endTime - startTime);

      // 更新指标
      this.metrics.successfulRequests++;
      this.updateAverageResponseTime(timing);
      this.metrics.totalDataTransferred += response.size;

      console.log(`✅ HTTP请求成功: ${url} (${timing}ms, ${(response.size / 1024).toFixed(1)}KB)`);

      return {
        ...response,
        timing
      };

    } catch (error) {
      const endTime = performance.now();
      const timing = Math.round(endTime - startTime);

      this.metrics.failedRequests++;

      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorName = error instanceof Error ? error.name : 'UnknownError';

      console.error(`❌ HTTP请求失败: ${url} (${timing}ms) - ${errorMessage}`);

      if (error instanceof HttpClientError) {
        throw error;
      }

      throw new HttpClientError(
        `Failed to fetch ${url}: ${errorMessage}`,
        undefined,
        errorName === 'TimeoutError',
        errorName === 'TypeError' && errorMessage.includes('fetch')
      );
    }
  }

  /**
   * 带重试的请求实现
   */
  private async fetchWithRetry(
    url: string, 
    options: Required<HttpClientOptions>
  ): Promise<Omit<HttpResponse, 'timing'>> {
    let lastError: Error | null = null;
    let redirectCount = 0;

    for (let attempt = 0; attempt <= options.maxRetries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), options.timeout);

        const response = await fetch(url, {
          headers: {
            ...this.defaultHeaders,
            'User-Agent': options.userAgent
          },
          redirect: options.followRedirects ? 'follow' : 'manual',
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new HttpClientError(
            `HTTP ${response.status}: ${response.statusText}`,
            response.status
          );
        }

        // 检查内容类型
        const contentType = response.headers.get('content-type') || '';
        if (!contentType.includes('text/html') && !contentType.includes('text/plain')) {
          throw new HttpClientError(
            `Invalid content type: ${contentType}. Expected HTML content.`,
            response.status
          );
        }

        const html = await response.text();
        
        // 检查内容大小
        if (html.length > 10 * 1024 * 1024) { // 10MB限制
          throw new HttpClientError(
            `Response too large: ${(html.length / 1024 / 1024).toFixed(1)}MB. Maximum allowed: 10MB.`
          );
        }

        // 提取响应头
        const headers: Record<string, string> = {};
        response.headers.forEach((value, key) => {
          headers[key] = value;
        });

        return {
          html,
          size: html.length,
          statusCode: response.status,
          headers,
          redirectCount
        };

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // 如果是最后一次尝试，直接抛出错误
        if (attempt === options.maxRetries) {
          break;
        }

        // 检查是否应该重试
        if (!this.shouldRetry(lastError)) {
          break;
        }

        // 等待后重试
        const errorMessage = lastError.message;
        console.warn(`🔄 HTTP请求重试 ${attempt + 1}/${options.maxRetries}: ${url} - ${errorMessage}`);
        await this.delay(options.retryDelay * Math.pow(2, attempt)); // 指数退避
      }
    }

    throw lastError || new Error('All retry attempts failed');
  }

  /**
   * 验证URL格式和安全性
   */
  private validateUrl(url: string): void {
    try {
      const parsedUrl = new URL(url);
      
      // 检查协议
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        throw new HttpClientError(`Unsupported protocol: ${parsedUrl.protocol}`);
      }

      // 检查主机名
      if (!parsedUrl.hostname) {
        throw new HttpClientError('Invalid hostname');
      }

      // 基础SSRF防护 - 阻止私有IP
      const hostname = parsedUrl.hostname.toLowerCase();
      if (this.isPrivateIP(hostname)) {
        throw new HttpClientError(`Access to private IP addresses is not allowed: ${hostname}`);
      }

    } catch (error) {
      if (error instanceof HttpClientError) {
        throw error;
      }
      throw new HttpClientError(`Invalid URL format: ${url}`);
    }
  }

  /**
   * 检查是否为私有IP地址
   */
  private isPrivateIP(hostname: string): boolean {
    // 检查localhost
    if (['localhost', '127.0.0.1', '::1'].includes(hostname)) {
      return true;
    }

    // 检查私有IP范围
    const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    const match = hostname.match(ipv4Regex);
    
    if (match) {
      const [, a, b] = match.map(Number);

      // 私有IP范围
      return (
        (a === 10) ||
        (a === 172 && b !== undefined && b >= 16 && b <= 31) ||
        (a === 192 && b === 168) ||
        (a === 169 && b === 254) // 链路本地地址
      );
    }

    return false;
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: Error): boolean {
    // 网络错误可以重试
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return true;
    }

    // 超时错误可以重试
    if (error.name === 'AbortError' || error.message.includes('AbortError')) {
      return true;
    }

    // 一般网络错误可以重试
    if (error.message.includes('Network error') || error.message.includes('network')) {
      return true;
    }

    // HTTP错误根据状态码判断
    if (error instanceof HttpClientError && error.statusCode) {
      // 5xx服务器错误可以重试
      return error.statusCode >= 500 && error.statusCode < 600;
    }

    return false;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 更新平均响应时间
   */
  private updateAverageResponseTime(newTime: number): void {
    const totalTime = this.metrics.avgResponseTime * this.metrics.successfulRequests;
    this.metrics.avgResponseTime = Math.round(
      (totalTime + newTime) / (this.metrics.successfulRequests + 1)
    );
  }

  /**
   * 获取性能指标
   */
  getMetrics(): HttpMetrics {
    return { ...this.metrics };
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      avgResponseTime: 0,
      totalDataTransferred: 0
    };
  }

  /**
   * 获取成功率
   */
  getSuccessRate(): number {
    if (this.metrics.totalRequests === 0) return 0;
    return Math.round((this.metrics.successfulRequests / this.metrics.totalRequests) * 100);
  }
}
