/**
 * 增强静态分析 - 结果分析引擎
 * 实现平台版本检测、置信度计算和数据完整性评估
 */

import type { VariableDetectionResult } from './variable-detector';

export interface ShoplineAnalysis {
  isShoplineStore: boolean;
  platformVersion: string;
  storeType: string;
  confidence: number;
  foundVariables: string[];
  totalVariablesFound: number;
  totalDataSize: number;
  completeness: number;
  detectionMethods: string[];
  analysisTimestamp: string;
  platformFeatures: string[];
  recommendations: string[];
}

export interface PlatformAnalysis {
  isShopline: boolean;
  version: string;
  storeType: string;
  features: string[];
  confidence: number;
}

export interface DataQualityMetrics {
  completeness: number;
  consistency: number;
  accuracy: number;
  freshness: number;
  overall: number;
}

export class ResultAnalysisEngine {
  private readonly PLATFORM_SIGNATURES = {
    'shopline-1.0': {
      requiredVariables: ['mainConfig'],
      optionalVariables: ['shopline'],
      features: ['legacy-api', 'basic-checkout', 'simple-analytics'],
      confidence: 0.9
    },
    'shopline-2.0': {
      requiredVariables: ['Shopline'],
      optionalVariables: ['__ENV__'],
      features: ['modern-api', 'advanced-checkout', 'enhanced-analytics', 'multi-currency'],
      confidence: 0.95
    },
    'mixed': {
      requiredVariables: [],
      optionalVariables: ['mainConfig', 'Shopline', '__ENV__', 'shopline'],
      features: ['hybrid-setup', 'migration-in-progress'],
      confidence: 0.7
    }
  };

  /**
   * 分析检测结果
   */
  analyzeDetectionResults(results: VariableDetectionResult[]): ShoplineAnalysis {
    const foundVariables = results.filter(r => r.found);
    
    // 平台版本检测
    const platformAnalysis = this.detectPlatformVersion(foundVariables);
    
    // 置信度计算
    const confidence = this.calculateOverallConfidence(foundVariables, platformAnalysis);
    
    // 数据完整性评估
    const completeness = this.assessDataCompleteness(foundVariables, platformAnalysis);
    
    // 数据质量指标
    const dataQuality = this.calculateDataQuality(foundVariables);
    
    // 生成建议
    const recommendations = this.generateRecommendations(platformAnalysis, foundVariables, dataQuality);

    return {
      isShoplineStore: platformAnalysis.isShopline,
      platformVersion: platformAnalysis.version,
      storeType: platformAnalysis.storeType,
      confidence,
      foundVariables: foundVariables.map(v => v.name),
      totalVariablesFound: foundVariables.length,
      totalDataSize: foundVariables.reduce((sum, v) => sum + (v.size || 0), 0),
      completeness,
      detectionMethods: [...new Set(foundVariables.map(v => v.detectionMethod))],
      analysisTimestamp: new Date().toISOString(),
      platformFeatures: platformAnalysis.features,
      recommendations
    };
  }

  /**
   * 检测平台版本
   */
  private detectPlatformVersion(foundVariables: VariableDetectionResult[]): PlatformAnalysis {
    const varNames = foundVariables.map(v => v.name);
    const avgConfidence = foundVariables.length > 0 
      ? foundVariables.reduce((sum, v) => sum + v.confidence, 0) / foundVariables.length 
      : 0;

    // 首先检查混合版本 (优先级最高)
    const hasV1 = varNames.some(name => ['mainConfig', 'shopline'].includes(name));
    const hasV2 = varNames.some(name => ['Shopline', '__ENV__'].includes(name));

    if (hasV1 && hasV2) {
      return {
        isShopline: true,
        version: 'Mixed',
        storeType: 'SHOPLINE Mixed Version Store',
        features: this.PLATFORM_SIGNATURES['mixed'].features,
        confidence: Math.min(avgConfidence, 0.8)
      };
    }

    // SHOPLINE 2.0 检测 (纯2.0，没有1.0变量)
    if (this.matchesPlatform(varNames, 'shopline-2.0') && !hasV1) {
      return {
        isShopline: true,
        version: '2.0',
        storeType: 'SHOPLINE 2.0 Store',
        features: this.PLATFORM_SIGNATURES['shopline-2.0'].features,
        confidence: Math.min(avgConfidence + 0.15, 1.0)
      };
    }

    // SHOPLINE 1.0 检测 (纯1.0，没有2.0变量)
    if (this.matchesPlatform(varNames, 'shopline-1.0') && !hasV2) {
      return {
        isShopline: true,
        version: '1.0',
        storeType: 'SHOPLINE 1.0 Store',
        features: this.PLATFORM_SIGNATURES['shopline-1.0'].features,
        confidence: Math.min(avgConfidence + 0.1, 1.0)
      };
    }
    
    // 部分检测
    if (varNames.some(name => ['mainConfig', 'Shopline', '__ENV__', 'shopline'].includes(name))) {
      return {
        isShopline: true,
        version: 'Unknown',
        storeType: 'SHOPLINE Store (Partial Detection)',
        features: ['partial-detection'],
        confidence: Math.min(avgConfidence * 0.8, 0.7)
      };
    }

    return {
      isShopline: false,
      version: 'N/A',
      storeType: 'Not a SHOPLINE Store',
      features: [],
      confidence: 0
    };
  }

  /**
   * 检查是否匹配特定平台
   */
  private matchesPlatform(varNames: string[], platform: keyof typeof this.PLATFORM_SIGNATURES): boolean {
    const signature = this.PLATFORM_SIGNATURES[platform];

    // 检查必需变量
    const hasRequiredVars = signature.requiredVariables.length === 0 ||
      signature.requiredVariables.every(varName => varNames.includes(varName));

    if (!hasRequiredVars) {
      return false;
    }

    // 检查可选变量 - 至少要有一个
    const hasOptionalVars = signature.optionalVariables.length === 0 ||
      signature.optionalVariables.some(varName => varNames.includes(varName));

    return hasOptionalVars;
  }

  /**
   * 计算总体置信度
   */
  private calculateOverallConfidence(
    foundVariables: VariableDetectionResult[], 
    platformAnalysis: PlatformAnalysis
  ): number {
    if (!platformAnalysis.isShopline || foundVariables.length === 0) {
      return 0;
    }

    // 基础置信度来自平台分析
    let confidence = platformAnalysis.confidence;

    // 基于检测方法的调整
    const methodWeights = {
      'direct-assignment': 1.0,
      'variable-declaration': 0.9,
      'json-parse': 0.95,
      'function-call': 0.7,
      'object-property': 0.8
    };

    const avgMethodConfidence = foundVariables.reduce((sum, v) => {
      const weight = methodWeights[v.detectionMethod as keyof typeof methodWeights] || 0.5;
      return sum + (v.confidence * weight);
    }, 0) / foundVariables.length;

    confidence = (confidence + avgMethodConfidence) / 2;

    // 基于数据大小的调整
    const totalDataSize = foundVariables.reduce((sum, v) => sum + (v.size || 0), 0);
    if (totalDataSize > 1000) {
      confidence += 0.05; // 大量数据增加置信度
    }

    // 基于变量数量的调整
    if (foundVariables.length >= 3) {
      confidence += 0.05; // 多个变量增加置信度
    }

    return Math.min(Math.max(confidence, 0), 1);
  }

  /**
   * 评估数据完整性
   */
  private assessDataCompleteness(
    foundVariables: VariableDetectionResult[], 
    platformAnalysis: PlatformAnalysis
  ): number {
    if (!platformAnalysis.isShopline) {
      return 0;
    }

    const varNames = foundVariables.map(v => v.name);
    let completeness = 0;

    if (platformAnalysis.version === '1.0') {
      // SHOPLINE 1.0 完整性检查
      const v1Variables = ['mainConfig', 'shopline'];
      const foundV1 = v1Variables.filter(v => varNames.includes(v)).length;
      completeness = foundV1 / v1Variables.length;
    } else if (platformAnalysis.version === '2.0') {
      // SHOPLINE 2.0 完整性检查
      const v2Variables = ['Shopline', '__ENV__'];
      const foundV2 = v2Variables.filter(v => varNames.includes(v)).length;
      completeness = foundV2 / v2Variables.length;
    } else {
      // 其他版本或混合版本
      const allVariables = ['mainConfig', 'shopline', 'Shopline', '__ENV__'];
      const foundAll = allVariables.filter(v => varNames.includes(v)).length;
      completeness = foundAll / allVariables.length;
    }

    return Math.min(Math.max(completeness, 0), 1);
  }

  /**
   * 计算数据质量指标
   */
  private calculateDataQuality(foundVariables: VariableDetectionResult[]): DataQualityMetrics {
    if (foundVariables.length === 0) {
      return {
        completeness: 0,
        consistency: 0,
        accuracy: 0,
        freshness: 1, // 新检测的数据认为是新鲜的
        overall: 0
      };
    }

    // 完整性：基于找到的变量数量
    const completeness = Math.min(foundVariables.length / 6, 1); // 6是总的目标变量数

    // 一致性：基于检测方法的一致性
    const methods = foundVariables.map(v => v.detectionMethod);
    const uniqueMethods = new Set(methods);
    const consistency = uniqueMethods.size <= 2 ? 1 : Math.max(0.5, 1 - (uniqueMethods.size - 2) * 0.2);

    // 准确性：基于平均置信度
    const accuracy = foundVariables.reduce((sum, v) => sum + v.confidence, 0) / foundVariables.length;

    // 新鲜度：新检测的数据认为是新鲜的
    const freshness = 1;

    // 总体质量
    const overall = (completeness + consistency + accuracy + freshness) / 4;

    return {
      completeness,
      consistency,
      accuracy,
      freshness,
      overall
    };
  }

  /**
   * 生成建议
   */
  private generateRecommendations(
    platformAnalysis: PlatformAnalysis,
    foundVariables: VariableDetectionResult[],
    dataQuality: DataQualityMetrics
  ): string[] {
    const recommendations: string[] = [];

    if (!platformAnalysis.isShopline) {
      recommendations.push('This does not appear to be a SHOPLINE store');
      return recommendations;
    }

    // 基于平台版本的建议
    if (platformAnalysis.version === '1.0') {
      recommendations.push('SHOPLINE 1.0 detected - consider upgrading to 2.0 for enhanced features');
    } else if (platformAnalysis.version === '2.0') {
      recommendations.push('SHOPLINE 2.0 detected - modern platform with full feature support');
    } else if (platformAnalysis.version === 'Mixed') {
      recommendations.push('Mixed version detected - migration may be in progress');
    }

    // 基于数据质量的建议
    if (dataQuality.completeness < 0.5) {
      recommendations.push('Low data completeness - some SHOPLINE variables may be missing');
    }

    if (dataQuality.consistency < 0.7) {
      recommendations.push('Inconsistent detection methods - manual verification recommended');
    }

    if (dataQuality.accuracy < 0.8) {
      recommendations.push('Low detection accuracy - results may need verification');
    }

    // 基于找到的变量的建议
    if (foundVariables.length >= 2) {
      recommendations.push('Comprehensive SHOPLINE integration detected');
    }

    return recommendations;
  }

  /**
   * 生成详细报告
   */
  generateDetailedReport(analysis: ShoplineAnalysis): string {
    const lines: string[] = [];
    
    lines.push('='.repeat(60));
    lines.push('SHOPLINE STORE ANALYSIS REPORT');
    lines.push('='.repeat(60));
    lines.push('');
    
    lines.push(`Store Type: ${analysis.storeType}`);
    lines.push(`Platform Version: ${analysis.platformVersion}`);
    lines.push(`Overall Confidence: ${(analysis.confidence * 100).toFixed(1)}%`);
    lines.push(`Data Completeness: ${(analysis.completeness * 100).toFixed(1)}%`);
    lines.push('');
    
    lines.push('Found Variables:');
    analysis.foundVariables.forEach(varName => {
      lines.push(`  ✓ ${varName}`);
    });
    lines.push('');
    
    lines.push('Detection Methods:');
    analysis.detectionMethods.forEach(method => {
      lines.push(`  • ${method}`);
    });
    lines.push('');
    
    lines.push('Platform Features:');
    analysis.platformFeatures.forEach(feature => {
      lines.push(`  • ${feature}`);
    });
    lines.push('');
    
    lines.push('Recommendations:');
    analysis.recommendations.forEach(rec => {
      lines.push(`  → ${rec}`);
    });
    lines.push('');
    
    lines.push(`Analysis completed at: ${analysis.analysisTimestamp}`);
    lines.push('='.repeat(60));
    
    return lines.join('\n');
  }
}
