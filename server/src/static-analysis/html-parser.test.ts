/**
 * HTML解析引擎单元测试
 *
 * 测试覆盖范围：
 * - HTML解析和脚本块提取
 * - 内联脚本和外部脚本处理
 * - 错误处理和边界条件
 * - 性能统计和元数据提取
 * - 复杂HTML结构解析
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { HtmlParser, HtmlParserError } from './html-parser';

describe('HtmlParser', () => {
  let parser: HtmlParser;

  beforeEach(() => {
    parser = new HtmlParser();
  });

  describe('parseHtml', () => {
    it('应该成功解析包含内联脚本的HTML', () => {
      const html = `
        <html>
          <head>
            <script>
              window.mainConfig = {"merchantId": "123"};
              var shoplineData = {version: "1.0"};
            </script>
          </head>
          <body>
            <script>
              console.log("Hello World");
            </script>
          </body>
        </html>
      `;

      const result = parser.parseHtml(html);

      expect(result.totalScripts).toBe(2);
      expect(result.inlineScripts).toBe(2);
      expect(result.externalScripts).toBe(0);
      expect(result.scriptBlocks).toHaveLength(2);
      
      const firstScript = result.scriptBlocks[0];
      expect(firstScript.type).toBe('inline');
      expect(firstScript.hasInlineCode).toBe(true);
      expect(firstScript.content).toContain('window.mainConfig');
    });

    it('应该正确识别外部脚本', () => {
      const parserWithExternal = new HtmlParser({ extractExternalScripts: true });

      const html = `
        <html>
          <head>
            <script src="https://example.com/script.js"></script>
            <script src="/local/script.js" type="text/javascript"></script>
          </head>
        </html>
      `;

      const result = parserWithExternal.parseHtml(html);

      expect(result.totalScripts).toBe(2);
      expect(result.inlineScripts).toBe(0);
      expect(result.externalScripts).toBe(2);

      result.scriptBlocks.forEach(script => {
        expect(script.type).toBe('external');
        expect(script.hasInlineCode).toBe(false);
        expect(script.src).toBeDefined();
      });
    });

    it('应该正确识别JSON脚本', () => {
      const html = `
        <html>
          <head>
            <script type="application/json">
              {"config": {"theme": "dark"}}
            </script>
            <script type="application/ld+json">
              {"@context": "https://schema.org"}
            </script>
          </head>
        </html>
      `;

      const result = parser.parseHtml(html);

      expect(result.totalScripts).toBe(2);
      result.scriptBlocks.forEach(script => {
        expect(script.type).toBe('json');
        expect(script.hasInlineCode).toBe(false);
      });
    });

    it('应该正确解析脚本属性', () => {
      const html = `
        <script type="module">
          import { test } from './module.js';
        </script>
      `;

      const result = parser.parseHtml(html);
      const script = result.scriptBlocks[0];

      expect(script.type).toBe('module');
      expect(script.attributes.type).toBe('module');
    });

    it('应该过滤太短的脚本', () => {
      const html = `
        <html>
          <script>x=1;</script>
          <script>
            window.mainConfig = {"merchantId": "123"};
          </script>
        </html>
      `;

      const result = parser.parseHtml(html);

      expect(result.totalScripts).toBe(1); // 只有长脚本被包含
      expect(result.scriptBlocks[0].content).toContain('mainConfig');
    });

    it('应该处理复杂的HTML结构', () => {
      const html = `
        <!DOCTYPE html>
        <html lang="en">
          <head>
            <meta charset="UTF-8">
            <title>Test</title>
            <script>
              // 注释
              window.config = {
                "api": "https://api.example.com",
                "features": ["cart", "checkout"]
              };
            </script>
          </head>
          <body>
            <div>Content</div>
            <script>
              function initApp() {
                console.log("App initialized");
              }
              initApp();
            </script>
          </body>
        </html>
      `;

      const result = parser.parseHtml(html);

      expect(result.totalScripts).toBe(2);
      expect(result.scriptBlocks.every(s => s.hasInlineCode)).toBe(true);
    });

    it('应该验证HTML格式', () => {
      expect(() => parser.parseHtml('')).toThrow(HtmlParserError);
      expect(() => parser.parseHtml('not html content')).toThrow(HtmlParserError);
      expect(() => parser.parseHtml(null as any)).toThrow(HtmlParserError);
    });

    it('应该处理没有脚本的HTML', () => {
      const html = `
        <html>
          <head><title>No Scripts</title></head>
          <body><p>Just content</p></body>
        </html>
      `;

      const result = parser.parseHtml(html);

      expect(result.totalScripts).toBe(0);
      expect(result.scriptBlocks).toHaveLength(0);
      expect(result.parseTime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('configuration', () => {
    it('应该支持自定义选项', () => {
      const customParser = new HtmlParser({
        minScriptLength: 5,
        includeJsonScripts: false
      });

      const html = `
        <html>
          <script>window.config = {test: true};</script>
          <script type="application/json">{"test": true}</script>
        </html>
      `;

      const result = customParser.parseHtml(html);

      expect(result.totalScripts).toBe(1); // 内联脚本被包含，JSON被排除
      expect(result.scriptBlocks.some(s => s.type === 'json')).toBe(false);
      expect(result.scriptBlocks.some(s => s.type === 'inline')).toBe(true);
    });
  });

  describe('utility methods', () => {
    it('应该生成脚本摘要', () => {
      const html = `
        <script>
          window.mainConfig = {
            "merchantId": "123",
            "features": ["cart", "checkout"]
          };
          
          function initShop() {
            console.log("Shop initialized");
          }
        </script>
      `;

      const result = parser.parseHtml(html);
      const summary = parser.getScriptSummary(result.scriptBlocks[0]);

      expect(summary.hasVariableAssignments).toBe(true);
      expect(summary.hasWindowAccess).toBe(true);
      expect(summary.lineCount).toBeGreaterThan(1);
      expect(summary.preview).toContain('window.mainConfig');
      expect(['low', 'medium', 'high']).toContain(summary.estimatedComplexity);
    });

    it('应该查找包含特定模式的脚本', () => {
      const html = `
        <script>
          window.mainConfig = {"test": true};
        </script>
        <script>
          window.Shopline = {"version": "2.0"};
        </script>
        <script>
          console.log("No config here");
        </script>
      `;

      const result = parser.parseHtml(html);
      const configScripts = parser.findScriptsWithPattern(
        result.scriptBlocks, 
        /window\.(mainConfig|Shopline)/
      );

      expect(configScripts).toHaveLength(2);
      expect(configScripts.every(s => 
        s.content.includes('mainConfig') || s.content.includes('Shopline')
      )).toBe(true);
    });

    it('应该计算解析统计信息', () => {
      const html = `
        <script>
          window.config = {"large": "data".repeat(100)};
        </script>
        <script>
          var small = 1;
        </script>
      `;

      const result = parser.parseHtml(html);
      const stats = parser.getParsingStats(result);

      expect(stats.efficiency).toBeGreaterThan(0);
      expect(stats.avgScriptSize).toBeGreaterThan(0);
      expect(stats.largestScript).toBeGreaterThan(stats.avgScriptSize);
      expect(stats.scriptDensity).toBeGreaterThan(0);
      expect(stats.scriptDensity).toBeLessThan(100);
    });
  });

  describe('edge cases', () => {
    it('应该处理嵌套的script标签内容', () => {
      const html = `
        <script>
          var htmlContent = '<script>alert("nested")</script>';
          window.config = {html: htmlContent};
        </script>
      `;

      const result = parser.parseHtml(html);

      expect(result.totalScripts).toBe(1);
      expect(result.scriptBlocks[0].content).toContain('htmlContent');
    });

    it('应该处理包含特殊字符的脚本', () => {
      const html = `
        <script>
          window.config = {
            "unicode": "测试中文",
            "special": "quotes'and\"symbols",
            "html": "<div>content</div>"
          };
        </script>
      `;

      const result = parser.parseHtml(html);

      expect(result.totalScripts).toBe(1);
      expect(result.scriptBlocks[0].content).toContain('测试中文');
      expect(result.scriptBlocks[0].content).toContain('quotes\'and"symbols');
    });

    it('应该处理格式不规范的HTML', () => {
      const html = `
        <HTML>
          <SCRIPT TYPE="text/javascript">
            WINDOW.CONFIG = {TEST: TRUE};
          </SCRIPT>
        </HTML>
      `;

      const result = parser.parseHtml(html);

      expect(result.totalScripts).toBe(1);
      expect(result.scriptBlocks[0].attributes.type).toBe('text/javascript');
    });
  });
});
