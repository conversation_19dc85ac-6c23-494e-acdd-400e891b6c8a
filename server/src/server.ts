/**
 * 简化的 ShopLine Checker 服务器
 * 专注于核心功能，移除过度复杂的特性
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { detectPageVariables, analyzeResults } from './core/detector';
import { browserManager } from './core/browser';
import { cacheManager } from './core/cache';
import { StaticAnalysisService } from './core/static-analysis-service';
import { hybridDetectionService } from './core/hybrid-detection-service';
import { config, log } from './config';
import {
  DetectRequest,
  ApiResponse,
  DetectResponseData,
  DEFAULT_SHOPLINE_VARIABLES,
  createSuccessResponse,
  createErrorResponse,
  validateDetectRequest,
  validateStaticAnalysisRequest,
  validateEnhancedDetectRequest
} from './types';

// 创建应用
const app = new Hono();

// 基础中间件
app.use('*', cors({
  origin: config.corsOrigin,
  allowMethods: ['GET', 'POST', 'OPTIONS'],
  allowHeaders: ['Content-Type']
}));

// 请求日志中间件
app.use('*', async (c, next) => {
  const start = Date.now();
  await next();
  const duration = Date.now() - start;
  log.info(`${c.req.method} ${c.req.path} - ${c.res.status} (${duration}ms)`);
});

/**
 * 健康检查
 */
app.get('/health', async (c) => {
  const browserStats = browserManager.getStats();
  const cacheStats = cacheManager.getStats();

  // 检查静态分析器健康状态
  let staticAnalysisHealthy = true;
  try {
    const staticService = new StaticAnalysisService();
    staticService.getStats();
    // 简单的健康检查 - 如果能获取统计信息就认为是健康的
    staticAnalysisHealthy = true;
  } catch (error) {
    staticAnalysisHealthy = false;
    log.warn('Static analysis health check failed', { error });
  }

  const browserHealthy = browserStats.hasActiveBrowser && browserStats.hasActivePage;
  const overallStatus = browserHealthy && staticAnalysisHealthy ? 'healthy' : 'degraded';

  return c.json(createSuccessResponse({
    status: overallStatus,
    timestamp: new Date().toISOString(),
    browser: browserStats,
    cache: cacheStats,
    staticAnalysis: {
      healthy: staticAnalysisHealthy
    }
  }));
});

/**
 * 通用检测端点
 */
app.post('/detect', async (c) => {
  try {
    const body = await c.req.json();
    const request = validateDetectRequest(body);
    
    if (!request) {
      return c.json(createErrorResponse('Invalid request format'), 400);
    }

    const result = await performDetection(request);
    return c.json(result.response, result.status as any);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    log.error('Detection request failed', { error: errorMessage });
    return c.json(createErrorResponse('Internal server error'), 500);
  }
});

/**
 * Shopline 专用检测端点
 */
app.post('/detect/shopline', async (c) => {
  try {
    const body = await c.req.json();
    
    if (!body.url) {
      return c.json(createErrorResponse('URL is required'), 400);
    }

    const request: DetectRequest = {
      url: body.url,
      variables: DEFAULT_SHOPLINE_VARIABLES,
      options: body.options || {}
    };

    const result = await performDetection(request);
    return c.json(result.response, result.status as any);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    log.error('Shopline detection request failed', { error: errorMessage });
    return c.json(createErrorResponse('Internal server error'), 500);
  }
});

/**
 * 获取统计信息
 */
app.get('/stats', async (c) => {
  const browserStats = browserManager.getStats();
  const cacheStats = cacheManager.getStats();

  // 获取静态分析统计信息
  const staticAnalysisService = new StaticAnalysisService();
  const staticStats = staticAnalysisService.getStats();

  // 获取检测模式选择统计
  const { detectionModeSelector } = await import('./core/detection-mode-selector');
  const modeSelectionStats = detectionModeSelector.getSelectionStats();

  return c.json(createSuccessResponse({
    browser: browserStats,
    cache: cacheStats,
    staticAnalysis: staticStats,
    modeSelection: modeSelectionStats,
    timestamp: new Date().toISOString()
  }));
});

/**
 * 清理缓存
 */
app.post('/cache/clear', async (c) => {
  try {
    await cacheManager.clear();
    return c.json(createSuccessResponse({ message: 'Cache cleared successfully' }));
  } catch (error) {
    log.error('Cache clear failed', error);
    return c.json(createErrorResponse('Failed to clear cache'), 500);
  }
});

/**
 * 浏览器健康检查
 */
app.post('/browser/health', async (c) => {
  try {
    const isHealthy = await browserManager.healthCheck();

    if (!isHealthy) {
      await browserManager.restart();
    }

    return c.json(createSuccessResponse({
      healthy: isHealthy,
      restarted: !isHealthy,
      stats: browserManager.getStats()
    }));
  } catch (error) {
    log.error('Browser health check failed', error);
    return c.json(createErrorResponse('Browser health check failed'), 500);
  }
});

// ============================================================================
// 静态分析 API 端点
// ============================================================================

/**
 * 静态分析端点
 */
app.post('/analyze/static', async (c) => {
  try {
    const body = await c.req.json();
    const request = validateStaticAnalysisRequest(body);

    if (!request) {
      log.warn('Invalid static analysis request', { body });
      return c.json(createErrorResponse('Invalid request format. Required: { url: string, options?: object }'), 400);
    }

    log.info('Starting static analysis', { url: request.url });
    const service = new StaticAnalysisService(request.options);
    const result = await service.analyze(request.url);

    if (!result.success) {
      log.warn('Static analysis failed', { url: request.url, error: result.error });
      return c.json(createErrorResponse(result.error || 'Static analysis failed'), 422);
    }

    log.info('Static analysis completed successfully', {
      url: request.url,
      foundVariables: result.analysis.foundCount,
      timing: result.timing.total
    });

    return c.json(createSuccessResponse(result));

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    log.error('Static analysis request failed', { error: errorMessage, stack: error instanceof Error ? error.stack : undefined });
    return c.json(createErrorResponse('Internal server error'), 500);
  }
});

/**
 * 批量静态分析端点
 */
app.post('/analyze/static/batch', async (c) => {
  try {
    const body = await c.req.json();

    if (!body.urls || !Array.isArray(body.urls) || body.urls.length === 0) {
      log.warn('Invalid batch request - missing URLs array', { body });
      return c.json(createErrorResponse('URLs array is required and must not be empty'), 400);
    }

    if (body.urls.length > 50) {
      log.warn('Batch request too large', { urlCount: body.urls.length });
      return c.json(createErrorResponse('Maximum 50 URLs allowed per batch request'), 400);
    }

    // 验证所有URL
    const invalidUrls: string[] = [];
    for (const url of body.urls) {
      if (!url || typeof url !== 'string') {
        invalidUrls.push(url);
        continue;
      }
      try {
        new URL(url);
      } catch {
        invalidUrls.push(url);
      }
    }

    if (invalidUrls.length > 0) {
      log.warn('Invalid URLs in batch request', { invalidUrls });
      return c.json(createErrorResponse(`Invalid URLs: ${invalidUrls.join(', ')}`), 400);
    }

    log.info('Starting batch static analysis', { urlCount: body.urls.length });
    const service = new StaticAnalysisService(body.options);
    const results = await service.analyzeBatch(body.urls);

    const summary = {
      total: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    };

    log.info('Batch static analysis completed', summary);

    return c.json(createSuccessResponse({
      results,
      summary
    }));

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    log.error('Batch static analysis request failed', {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });
    return c.json(createErrorResponse('Internal server error'), 500);
  }
});

// ============================================================================
// 增强检测 API 端点
// ============================================================================

/**
 * 增强检测端点 - 支持多种检测模式
 */
app.post('/detect/enhanced', async (c) => {
  try {
    const body = await c.req.json();
    const request = validateEnhancedDetectRequest(body);

    if (!request) {
      log.warn('Invalid enhanced detection request', { body });
      return c.json(createErrorResponse('Invalid request format. Required: { url: string, variables?: array, options?: object }'), 400);
    }

    log.info('Starting enhanced detection', {
      url: request.url,
      mode: request.options?.mode || 'auto',
      variableCount: request.variables?.length || 0
    });

    const result = await hybridDetectionService.performEnhancedDetection(request);

    if (!result.success) {
      log.warn('Enhanced detection failed', { url: request.url, error: result.error });
      return c.json(createErrorResponse(result.error || 'Enhanced detection failed'), 422);
    }

    log.info('Enhanced detection completed successfully', {
      url: request.url,
      method: result.method,
      foundVariables: result.analysis.foundCount,
      timing: result.timing.total,
      isHybrid: result.method === 'hybrid'
    });

    return c.json(createSuccessResponse(result));

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    log.error('Enhanced detection request failed', {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });
    return c.json(createErrorResponse('Internal server error'), 500);
  }
});

// ============================================================================
// 核心检测函数
// ============================================================================

/**
 * 执行检测
 */
async function performDetection(request: DetectRequest): Promise<{
  response: ApiResponse<DetectResponseData>;
  status: number;
}> {
  const { url, variables = DEFAULT_SHOPLINE_VARIABLES, options = {} } = request;
  
  try {
    // 检查缓存
    const variableNames = variables.map((v: any) => v.name);
    const cachedResults = await cacheManager.get(url, variableNames);
    
    if (cachedResults) {
      const analysis = analyzeResults(cachedResults);
      
      const responseData: DetectResponseData = {
        url,
        results: cachedResults,
        analysis,
        cache: { hit: true, detectionTime: 0 },
        timestamp: new Date().toISOString()
      };
      
      return {
        response: createSuccessResponse(responseData),
        status: 200
      };
    }

    // 执行检测
    const startTime = Date.now();
    const page = await browserManager.getPage();
    
    try {
      const results = await detectPageVariables(page, url, variables, options);
      const detectionTime = Date.now() - startTime;
      
      // 缓存结果（只缓存成功的检测结果）
      const hasValidResults = results.some((r: any) => r.found);
      if (hasValidResults) {
        await cacheManager.set(url, variableNames, results);
      }
      
      const analysis = analyzeResults(results);
      
      const responseData: DetectResponseData = {
        url,
        results,
        analysis,
        cache: { hit: false, detectionTime },
        timestamp: new Date().toISOString()
      };
      
      return {
        response: createSuccessResponse(responseData),
        status: 200
      };
      
    } finally {
      await browserManager.releasePage(page);
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    log.error(`Detection failed for ${url}`, { error: errorMessage });
    
    return {
      response: createErrorResponse(`Detection failed: ${errorMessage}`),
      status: 500
    };
  }
}

const port = config.port;

console.log(`🚀 Starting ShopLine Checker Server on port ${port}`);
console.log(`📋 Environment: ${process.env['NODE_ENV'] || 'development'}`);
console.log(`🌐 CORS Origin: ${config.corsOrigin}`);

// 如果直接运行此文件，启动服务器
if (require.main === module) {
  const { serve } = require('@hono/node-server');

  serve({
    fetch: app.fetch,
    port: port
  }, (info: any) => {
    console.log(`✅ Server running on http://localhost:${info.port}`);
    console.log('📋 Available endpoints:');
    console.log('  GET  /health - Health check');
    console.log('  POST /detect - General detection');
    console.log('  POST /detect/shopline - Shopline detection');
    console.log('  POST /detect/enhanced - Enhanced detection (multi-mode)');
    console.log('  POST /analyze/static - Static analysis');
    console.log('  POST /analyze/static/batch - Batch static analysis');
    console.log('  GET  /stats - Statistics');
    console.log('  POST /cache/clear - Clear cache');
    console.log('  POST /browser/health - Browser health check');
  });
}

export default {
  port,
  fetch: app.fetch
};
