/**
 * 简化的配置管理
 */

export const config = {
  port: parseInt(process.env['PORT'] || '3000'),
  browserTimeout: parseInt(process.env['BROWSER_TIMEOUT'] || '30000'),
  maxConcurrency: parseInt(process.env['MAX_CONCURRENCY'] || '3'),
  corsOrigin: process.env['CORS_ORIGIN'] || '*',
  // 是否记录页面脚本错误（默认关闭，专注于核心功能）
  logPageErrors: process.env['LOG_PAGE_ERRORS'] === 'true'
};

// 简单的日志函数
export const log = {
  info: (message: string, data?: any) => {
    console.log(`[INFO] ${new Date().toISOString()} ${message}`, data || '');
  },
  error: (message: string, error?: any) => {
    console.error(`[ERROR] ${new Date().toISOString()} ${message}`, error || '');
  },
  warn: (message: string, data?: any) => {
    console.warn(`[WARN] ${new Date().toISOString()} ${message}`, data || '');
  }
};
