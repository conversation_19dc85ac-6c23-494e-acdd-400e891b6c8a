/**
 * 核心类型定义
 * 简化版本，只包含必要的类型
 */

// ============================================================================
// 基础 API 类型
// ============================================================================

/**
 * 通用 API 响应结构
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

// ============================================================================
// 检测相关类型
// ============================================================================

/**
 * 变量配置
 */
export interface VariableConfig {
  /** 变量名称 */
  name: string;

  /** 变量路径（如 window.Shopline） */
  path: string;

  /** 描述信息（可选） */
  description?: string;
}

/**
 * 变量检测结果
 */
export interface VariableResult {
  /** 变量名称 */
  name: string;

  /** 是否找到 */
  found: boolean;

  /** 变量值 */
  value: any;

  /** 变量类型 */
  type: string;

  /** 数据大小 */
  size: number;

  /** 错误信息（如果有） */
  error?: string;
}

/**
 * 检测选项
 */
export interface DetectionOptions {
  /** 超时时间（毫秒） */
  timeout?: number;

  /** 等待时间（毫秒） */
  waitTime?: number;

  /** 用户代理 */
  userAgent?: string;

  /** 视口大小 */
  viewport?: {
    width: number;
    height: number;
  };
}

/**
 * 静态分析选项
 */
export interface StaticAnalysisOptions {
  /** HTTP客户端选项 */
  http?: {
    timeout?: number;
    maxRetries?: number;
    retryDelay?: number;
    userAgent?: string;
    followRedirects?: boolean;
  };

  /** HTML解析选项 */
  html?: {
    extractExternalScripts?: boolean;
    minScriptLength?: number;
    maxScriptLength?: number;
    includeJsonScripts?: boolean;
  };

  /** 启用详细报告 */
  enableDetailedReport?: boolean;

  /** 启用性能指标 */
  enablePerformanceMetrics?: boolean;
}

/**
 * 请求过滤选项
 */
export interface RequestFilterOptions {
  /** 启用请求过滤 */
  enableFiltering?: boolean;

  /** 阻止分析服务请求 */
  blockAnalytics?: boolean;

  /** 阻止广告服务请求 */
  blockAds?: boolean;

  /** 记录被阻止的请求 */
  logBlocked?: boolean;

  /** 自定义阻止的域名列表 */
  customBlockedDomains?: string[];
}

/**
 * 增强检测选项
 */
export interface EnhancedDetectionOptions extends DetectionOptions {
  /** 检测模式 */
  mode?: 'auto' | 'static' | 'dynamic' | 'hybrid';

  /** 静态分析选项 */
  staticOptions?: StaticAnalysisOptions;

  /** 启用结果对比 */
  enableComparison?: boolean;

  /** 置信度阈值 */
  confidenceThreshold?: number;
}

/**
 * 检测请求
 */
export interface DetectRequest {
  /** 目标 URL */
  url: string;

  /** 要检测的变量列表 */
  variables?: VariableConfig[];

  /** 检测选项 */
  options?: DetectionOptions;
}

/**
 * 增强检测请求
 */
export interface EnhancedDetectRequest {
  /** 目标 URL */
  url: string;

  /** 要检测的变量列表 */
  variables?: VariableConfig[];

  /** 增强检测选项 */
  options?: EnhancedDetectionOptions;
}

/**
 * 静态分析请求
 */
export interface StaticAnalysisRequest {
  /** 目标 URL */
  url: string;

  /** 静态分析选项 */
  options?: StaticAnalysisOptions;
}

/**
 * 检测响应数据 (保持向后兼容)
 */
export interface DetectResponseData {
  /** 目标 URL */
  url: string;

  /** 检测结果 */
  results: VariableResult[];

  /** 结果分析 */
  analysis: {
    foundCount: number;
    totalCount: number;
    foundVariables: string[];
    missingVariables: string[];
    hasShopline: boolean;
    summary: string;
  };

  /** 缓存信息 */
  cache?: {
    hit: boolean;
    detectionTime: number;
  };

  /** 时间戳 */
  timestamp: string;
}

// ============================================================================
// 统一检测类型 (新增)
// ============================================================================

/**
 * 检测方法类型
 */
export type DetectionMethod = 'dynamic' | 'static-analysis' | 'hybrid';

/**
 * 增强的分析结果
 */
export interface EnhancedAnalysis {
  // 保持向后兼容的字段
  foundCount: number;
  totalCount: number;
  foundVariables: string[];
  missingVariables: string[];
  hasShopline: boolean;
  summary: string;

  // 新增静态分析字段
  platformVersion?: string;
  storeType?: string;
  confidence?: number;
  totalDataSize?: number;
  completeness?: number;
  detectionMethods?: string[];
  platformFeatures?: string[];
  recommendations?: string[];
}

/**
 * 统一的时间信息
 */
export interface UnifiedTiming {
  total: number;
  detection: number;
  analysis: number;
  breakdown?: {
    httpFetch?: number;
    htmlParsing?: number;
    variableDetection?: number;
    resultAnalysis?: number;
    pageLoad?: number;
    scriptExecution?: number;
  };
}

/**
 * 缓存信息
 */
export interface CacheInfo {
  hit: boolean;
  detectionTime: number;
  key?: string;
  ttl?: number;
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  httpMetrics?: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    avgResponseTime: number;
    totalDataTransferred: number;
  };
  parsingStats?: {
    totalScripts: number;
    inlineScripts: number;
    externalScripts: number;
    parseTime: number;
  };
  detectionStats?: {
    strategiesUsed: string[];
    successfulDetections: number;
    averageConfidence: number;
  };
}

/**
 * 统一的检测结果
 */
export interface UnifiedDetectionResult {
  success: boolean;
  method: DetectionMethod;
  url: string;
  timing: UnifiedTiming;
  results: VariableResult[];
  analysis: EnhancedAnalysis;
  cache?: CacheInfo;
  performanceMetrics?: PerformanceMetrics;
  timestamp: string;
  error?: string;
}

// ============================================================================
// 默认变量配置
// ============================================================================

/**
 * 默认的 SHOPLINE 检测变量
 * 包含 SHOPLINE 1.0 和 2.0 平台的核心变量
 */
export const DEFAULT_SHOPLINE_VARIABLES: VariableConfig[] = [
  // SHOPLINE 2.0 平台变量
  {
    name: 'Shopline',
    path: 'window.Shopline',
    description: 'SHOPLINE 2.0 主对象'
  },
  {
    name: '__ENV__',
    path: 'window.__ENV__',
    description: 'SHOPLINE 2.0 环境配置'
  },
  // SHOPLINE 1.0 平台变量
  {
    name: 'mainConfig',
    path: 'window.mainConfig',
    description: 'SHOPLINE 1.0 主配置对象'
  },
  {
    name: 'shopline',
    path: 'window.shopline',
    description: 'SHOPLINE 1.0 对象（小写）'
  }
];

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 创建成功响应
 */
export function createSuccessResponse<T>(data: T): ApiResponse<T> {
  return {
    success: true,
    data,
    timestamp: new Date().toISOString()
  };
}

/**
 * 创建错误响应
 */
export function createErrorResponse(error: string): ApiResponse {
  return {
    success: false,
    error,
    timestamp: new Date().toISOString()
  };
}

/**
 * 验证检测请求
 */
export function validateDetectRequest(body: any): DetectRequest | null {
  if (!body || typeof body !== 'object') {
    return null;
  }

  if (!body.url || typeof body.url !== 'string') {
    return null;
  }

  // URL 基础验证
  try {
    new URL(body.url);
  } catch {
    return null;
  }

  return {
    url: body.url,
    variables: body.variables || DEFAULT_SHOPLINE_VARIABLES,
    options: body.options || {}
  };
}

/**
 * 验证增强检测请求
 */
export function validateEnhancedDetectRequest(body: any): EnhancedDetectRequest | null {
  if (!body || typeof body !== 'object') {
    return null;
  }

  if (!body.url || typeof body.url !== 'string') {
    return null;
  }

  // URL 基础验证
  try {
    new URL(body.url);
  } catch {
    return null;
  }

  // 验证检测模式
  const validModes = ['auto', 'static', 'dynamic', 'hybrid'];
  if (body.options?.mode && !validModes.includes(body.options.mode)) {
    return null;
  }

  return {
    url: body.url,
    variables: body.variables || DEFAULT_SHOPLINE_VARIABLES,
    options: body.options || {}
  };
}

/**
 * 验证静态分析请求
 */
export function validateStaticAnalysisRequest(body: any): StaticAnalysisRequest | null {
  if (!body || typeof body !== 'object') {
    return null;
  }

  if (!body.url || typeof body.url !== 'string') {
    return null;
  }

  // URL 基础验证
  try {
    new URL(body.url);
  } catch {
    return null;
  }

  return {
    url: body.url,
    options: body.options || {}
  };
}
