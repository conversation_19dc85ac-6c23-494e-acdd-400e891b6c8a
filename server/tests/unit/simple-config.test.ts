/**
 * 简单配置测试
 */

import { describe, it, expect } from 'vitest';
import { appConfig } from '@/config';

describe('Simple Config Tests', () => {
  it('should have app name', () => {
    expect(appConfig.app.name).toBe('shopline-checker-server');
  });

  it('should have app version', () => {
    expect(appConfig.app.version).toBe('1.0.0');
  });

  it('should have valid port', () => {
    expect(typeof appConfig.app.port).toBe('number');
    expect(appConfig.app.port).toBeGreaterThan(0);
  });

  it('should have browser configuration', () => {
    expect(appConfig.browser).toBeDefined();
    expect(appConfig.browser.pool).toBeDefined();
    expect(appConfig.browser.pool.min).toBeGreaterThan(0);
  });

  it('should have cache configuration', () => {
    expect(appConfig.cache).toBeDefined();
    expect(typeof appConfig.cache.ttl).toBe('number');
    expect(appConfig.cache.ttl).toBeGreaterThan(0);
  });

  it('should have security configuration', () => {
    expect(appConfig.security).toBeDefined();
    expect(typeof appConfig.security.jwtSecret).toBe('string');
    expect(appConfig.security.jwtSecret.length).toBeGreaterThan(0);
  });
});
