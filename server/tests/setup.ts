/**
 * Vitest 测试设置文件 - 简化版
 * 配置测试环境和全局设置
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';

// 设置测试环境变量

// 设置测试环境变量
process.env['NODE_ENV'] = 'test';
process.env['LOG_LEVEL'] = 'error';
process.env['REDIS_URL'] = 'redis://localhost:6379/1';
process.env['MONGODB_URL'] = 'mongodb://localhost:27017/shopline-checker-test';
process.env['BROWSER_POOL_MIN'] = '1';
process.env['BROWSER_POOL_MAX'] = '2';
process.env['CACHE_ENABLE_REDIS'] = 'false';
process.env['RATE_LIMIT_MAX_REQUESTS'] = '1000';
process.env['API_KEY_REQUIRED'] = 'false';

// Vitest 的超时时间在配置文件中设置

// 全局测试钩子
beforeAll(async () => {
  // 测试开始前的全局设置
  console.log('🧪 Starting test suite...');
});

afterAll(async () => {
  // 测试结束后的全局清理
  console.log('✅ Test suite completed');
});

// 每个测试前的设置
beforeEach(() => {
  // Vitest 会自动清理模拟
});

// 每个测试后的清理
afterEach(() => {
  // Vitest 会自动清理定时器
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Vitest 提供更好的 console 处理，无需手动模拟

// 导出测试工具函数
export const testUtils = {
  /**
   * 等待指定时间
   */
  wait: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * 创建模拟的 URL
   */
  createMockUrl: (path: string = ''): string => {
    return `https://example.com${path}`;
  },

  /**
   * 创建模拟的变量配置
   */
  createMockVariableConfig: (overrides: any = {}) => {
    return {
      name: 'testVar',
      path: 'window.testVar',
      type: 'string',
      defaultValue: 'default',
      ...overrides
    };
  },

  /**
   * 创建模拟的检测选项
   */
  createMockDetectionOptions: (overrides: any = {}) => {
    return {
      timeout: 10000,
      waitUntil: 'domcontentloaded',
      ...overrides
    };
  }
};
