{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "**/*.test.ts", "**/*.spec.ts"]}