# 生产环境配置
NODE_ENV=production
PORT=3000

# 日志配置
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true

# 数据库配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

MONGODB_URL=mongodb://localhost:27017/shopline-checker

# 安全配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
API_KEY_REQUIRED=true
CORS_ORIGIN=https://yourdomain.com

# 浏览器池配置
BROWSER_POOL_MIN=2
BROWSER_POOL_MAX=10
BROWSER_TIMEOUT=30000
BROWSER_HEADLESS=true

# 缓存配置
CACHE_TTL=300
CACHE_MAX_SIZE=1000
CACHE_ENABLE_REDIS=true

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 监控配置
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
