# 应用配置
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# 数据库配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

MONGODB_URL=mongodb://localhost:27017/shopline-checker
MONGODB_USERNAME=
MONGODB_PASSWORD=

# 浏览器池配置
BROWSER_POOL_MIN=2
BROWSER_POOL_MAX=10
BROWSER_MAX_PAGES_PER_INSTANCE=5
BROWSER_IDLE_TIMEOUT=300000
BROWSER_HEALTH_CHECK_INTERVAL=30000
BROWSER_TIMEOUT=30000
PAGE_TIMEOUT=15000

# 安全配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
API_KEY_REQUIRED=false
CORS_ORIGIN=*
ALLOWED_DOMAINS=

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL=false

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
CACHE_ENABLE_REDIS=true
CACHE_KEY_PREFIX=shopline-checker:

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs
LOG_MAX_SIZE=20m
LOG_MAX_FILES=30d

# 监控配置
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
PERFORMANCE_MONITORING=true

# 并发控制
MAX_CONCURRENT_REQUESTS=50
QUEUE_SIZE=200
QUEUE_TIMEOUT=60000

# 重试配置
RETRY_MAX_ATTEMPTS=3
RETRY_BASE_DELAY=1000
RETRY_MAX_DELAY=10000
RETRY_BACKOFF_FACTOR=2
RETRY_JITTER=true

# Puppeteer 配置
PUPPETEER_EXECUTABLE_PATH=
PUPPETEER_HEADLESS=true
PUPPETEER_ARGS=--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage

# 资源优化
BLOCK_IMAGES=true
BLOCK_STYLESHEETS=false
BLOCK_FONTS=true
BLOCK_MEDIA=true
BLOCK_ADS=true

# 安全配置
BLOCK_PRIVATE_IPS=true
ALLOWED_PROTOCOLS=http,https
MAX_URL_LENGTH=2048
MAX_VARIABLE_DEPTH=10

# 开发配置
DEBUG=false
ENABLE_SWAGGER=true
ENABLE_PLAYGROUND=false
