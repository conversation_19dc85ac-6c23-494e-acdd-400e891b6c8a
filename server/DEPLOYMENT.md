# ShopLine Checker Server 部署指南

本指南介绍如何使用 PM2 在生产环境中部署 ShopLine Checker Server。

## 🚀 快速部署

### 1. 环境准备

```bash
# 安装 Node.js 20+
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2
npm install -g pm2

# 安装 Redis
sudo apt-get install redis-server

# 安装 MongoDB
# 参考官方文档: https://docs.mongodb.com/manual/installation/
```

### 2. 项目部署

```bash
# 克隆项目
git clone <your-repo-url>
cd shopline-checker/server

# 安装依赖
npm ci --only=production

# 配置环境变量
cp .env.production .env
# 编辑 .env 文件，配置数据库连接等

# 构建应用
npm run build

# 启动应用
./scripts/start.sh
```

### 3. 验证部署

```bash
# 检查应用状态
npm run pm2:status

# 查看日志
npm run pm2:logs

# 测试 API
curl http://localhost:3000/health
```

## 📋 详细配置

### 环境变量配置

编辑 `.env` 文件，配置以下关键参数：

```bash
# 基础配置
NODE_ENV=production
PORT=3000

# 数据库配置
REDIS_URL=redis://localhost:6379
MONGODB_URL=mongodb://localhost:27017/shopline-checker

# 安全配置
JWT_SECRET=your-super-secret-jwt-key
API_KEY_REQUIRED=true
CORS_ORIGIN=https://yourdomain.com

# 浏览器配置
BROWSER_POOL_MIN=2
BROWSER_POOL_MAX=10
BROWSER_HEADLESS=true
```

### PM2 配置说明

`ecosystem.config.js` 文件包含以下主要配置：

- **集群模式**: 自动利用所有 CPU 核心
- **自动重启**: 应用崩溃时自动重启
- **内存监控**: 超过 1GB 内存时重启
- **日志管理**: 统一日志收集
- **环境变量**: 支持多环境配置

### 系统服务配置

设置 PM2 开机自启：

```bash
# 保存当前进程列表
pm2 save

# 生成启动脚本
pm2 startup

# 按照提示执行生成的命令（通常需要 sudo）
```

## 🔧 运维管理

### 日常操作

```bash
# 查看应用状态
pm2 status

# 重启应用
pm2 restart shopline-checker-server

# 停止应用
pm2 stop shopline-checker-server

# 查看日志
pm2 logs shopline-checker-server

# 实时监控
pm2 monit
```

### 更新部署

```bash
# 拉取最新代码
git pull origin main

# 运行部署脚本
./scripts/deploy.sh production
```

### 备份和恢复

```bash
# 备份 PM2 配置
pm2 dump

# 恢复 PM2 配置
pm2 resurrect
```

## 🔍 故障排查

### 常见问题

1. **应用无法启动**
   - 检查环境变量配置
   - 查看错误日志: `pm2 logs shopline-checker-server --err`
   - 验证数据库连接

2. **内存使用过高**
   - 调整 PM2 内存限制
   - 检查内存泄漏
   - 优化浏览器池配置

3. **性能问题**
   - 监控 CPU 使用率: `pm2 monit`
   - 调整集群实例数量
   - 优化缓存配置

### 日志分析

```bash
# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/combined.log

# 搜索特定错误
grep "ERROR" logs/*.log
```

## 🛡️ 安全建议

1. **防火墙配置**
   ```bash
   # 只开放必要端口
   sudo ufw allow 22    # SSH
   sudo ufw allow 3000  # 应用端口
   sudo ufw enable
   ```

2. **SSL/TLS 配置**
   - 使用 Nginx 作为反向代理
   - 配置 SSL 证书
   - 启用 HTTPS 重定向

3. **定期更新**
   - 定期更新系统包
   - 更新 Node.js 和 npm
   - 更新应用依赖

## 📊 监控和告警

建议集成以下监控工具：

- **PM2 Plus**: PM2 官方监控平台
- **Prometheus + Grafana**: 自建监控系统
- **New Relic**: 应用性能监控
- **Sentry**: 错误追踪和监控

## 🔄 CI/CD 集成

可以将部署脚本集成到 CI/CD 流水线中：

```yaml
# GitHub Actions 示例
deploy:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v2
    - name: Deploy to production
      run: |
        ssh user@server 'cd /path/to/app && git pull && ./scripts/deploy.sh production'
```
