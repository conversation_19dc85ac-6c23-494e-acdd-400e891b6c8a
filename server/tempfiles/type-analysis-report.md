# 类型定义差异分析报告

## 📊 主要类型对比

### 1. 响应数据结构差异

#### 主服务器 - DetectResponseData
```typescript
interface DetectResponseData {
  url: string;
  results: VariableResult[];
  analysis: {
    foundCount: number;
    totalCount: number;
    foundVariables: string[];
    missingVariables: string[];
    hasShopline: boolean;
    summary: string;
  };
  cache?: {
    hit: boolean;
    detectionTime: number;
  };
  timestamp: string;
}
```

#### 静态分析 - StaticAnalysisResult
```typescript
interface StaticAnalysisResult {
  success: boolean;
  method: 'static-analysis';
  url: string;
  timing: {
    total: number;
    httpFetch: number;
    htmlParsing: number;
    variableDetection: number;
    resultAnalysis: number;
  };
  analysis: ShoplineAnalysis;
  detailedReport?: string;
  performanceMetrics?: object;
  error?: string;
}
```

### 2. 分析结果结构差异

#### 主服务器分析结构 (简单)
```typescript
analysis: {
  foundCount: number;
  totalCount: number;
  foundVariables: string[];
  missingVariables: string[];
  hasShopline: boolean;
  summary: string;
}
```

#### 静态分析结构 (详细)
```typescript
interface ShoplineAnalysis {
  isShoplineStore: boolean;
  platformVersion: string;
  storeType: string;
  confidence: number;
  foundVariables: string[];
  totalVariablesFound: number;
  totalDataSize: number;
  completeness: number;
  detectionMethods: string[];
  analysisTimestamp: string;
  platformFeatures: string[];
  recommendations: string[];
}
```

## 🔍 关键差异点

### 1. 响应格式不一致
- **主服务器**: 直接返回 DetectResponseData
- **静态分析**: 包装在 success/error 结构中

### 2. 时间信息差异
- **主服务器**: 简单的 detectionTime
- **静态分析**: 详细的分阶段计时

### 3. 分析深度差异
- **主服务器**: 基础统计信息
- **静态分析**: 深度分析（平台版本、特性、建议）

### 4. 错误处理差异
- **主服务器**: 使用 ApiResponse 包装
- **静态分析**: 内置 error 字段

## 📋 需要统一的接口

### 1. 统一响应类型
需要创建兼容两种检测方式的统一响应类型

### 2. 统一分析结果
需要扩展现有分析结构，支持静态分析的详细信息

### 3. 统一时间信息
需要标准化时间和性能指标的表示方式

### 4. 统一错误处理
需要统一错误类型和处理机制

## 🎯 解决方案

### 1. 创建 UnifiedDetectionResult
```typescript
interface UnifiedDetectionResult {
  success: boolean;
  method: 'dynamic' | 'static-analysis' | 'hybrid';
  url: string;
  timing: {
    total: number;
    detection: number;
    analysis: number;
    breakdown?: {
      httpFetch?: number;
      htmlParsing?: number;
      variableDetection?: number;
      resultAnalysis?: number;
    };
  };
  results: VariableResult[];
  analysis: EnhancedAnalysis;
  cache?: CacheInfo;
  performanceMetrics?: PerformanceMetrics;
  timestamp: string;
  error?: string;
}
```

### 2. 扩展分析结果类型
```typescript
interface EnhancedAnalysis {
  // 保持向后兼容
  foundCount: number;
  totalCount: number;
  foundVariables: string[];
  missingVariables: string[];
  hasShopline: boolean;
  summary: string;
  
  // 新增静态分析字段
  platformVersion?: string;
  storeType?: string;
  confidence?: number;
  totalDataSize?: number;
  completeness?: number;
  detectionMethods?: string[];
  platformFeatures?: string[];
  recommendations?: string[];
}
```

## ✅ 兼容性策略

1. **向后兼容**: 保持现有 API 响应格式不变
2. **渐进增强**: 新字段为可选，逐步添加
3. **类型安全**: 使用 TypeScript 确保类型安全
4. **统一包装**: 使用适配器模式转换不同格式

## 📝 实施建议

1. 先扩展 types.ts，添加统一类型定义
2. 创建适配器函数，转换静态分析结果
3. 保持现有 API 不变，新增增强版 API
4. 逐步迁移到统一格式
