/**
 * 浏览器池集成测试
 * 验证浏览器池在实际并发场景中的性能提升
 */

const { detectPageVariables } = require('../dist/simple-detector');

async function testBrowserPoolIntegration() {
  console.log('🧪 浏览器池集成测试\n');

  const testUrl = 'https://bottle-demo.myshopline.com/';
  const variables = [
    { name: 'Shopline', path: 'window.Shopline' },
    { name: 'mainConfig', path: 'window.mainConfig' }
  ];

  console.log(`🌐 测试URL: ${testUrl}`);
  console.log(`🔍 检测变量: ${variables.map(v => v.name).join(', ')}\n`);

  // 测试1: 单个请求性能
  console.log('📋 测试1: 单个请求性能');
  const startTime1 = Date.now();
  
  try {
    const result1 = await detectPageVariables(testUrl, variables, { timeout: 15000 });
    const duration1 = Date.now() - startTime1;
    
    console.log(`⏱️ 单个请求耗时: ${duration1}ms`);
    console.log(`🔍 检测结果: ${result1.filter(r => r.found).length}/${result1.length} 个变量找到`);

    // 测试2: 并发请求性能
    console.log('\n📋 测试2: 并发请求性能 (3个并发)');
    const startTime2 = Date.now();
    
    const concurrentPromises = [
      detectPageVariables(testUrl, variables, { timeout: 15000 }),
      detectPageVariables(testUrl, variables, { timeout: 15000 }),
      detectPageVariables(testUrl, variables, { timeout: 15000 })
    ];
    
    const concurrentResults = await Promise.all(concurrentPromises);
    const duration2 = Date.now() - startTime2;
    
    console.log(`⏱️ 3个并发请求总耗时: ${duration2}ms`);
    console.log(`📊 平均每个请求: ${Math.round(duration2 / 3)}ms`);
    
    // 验证结果一致性
    let consistencyPassed = true;
    const firstResult = concurrentResults[0];
    
    for (let i = 1; i < concurrentResults.length; i++) {
      const currentResult = concurrentResults[i];
      
      if (firstResult.length !== currentResult.length) {
        consistencyPassed = false;
        break;
      }
      
      for (let j = 0; j < firstResult.length; j++) {
        if (firstResult[j].name !== currentResult[j].name || 
            firstResult[j].found !== currentResult[j].found) {
          consistencyPassed = false;
          break;
        }
      }
    }
    
    if (consistencyPassed) {
      console.log(`✅ 并发结果一致性验证通过`);
    } else {
      console.log(`❌ 并发结果一致性验证失败`);
    }

    // 测试3: 连续请求性能（测试池化效果）
    console.log('\n📋 测试3: 连续请求性能 (5个连续请求)');
    const sequentialTimes = [];
    
    for (let i = 0; i < 5; i++) {
      const startTime = Date.now();
      await detectPageVariables(testUrl, variables, { timeout: 15000 });
      const duration = Date.now() - startTime;
      sequentialTimes.push(duration);
      console.log(`   请求 ${i + 1}: ${duration}ms`);
    }
    
    const avgSequentialTime = Math.round(sequentialTimes.reduce((sum, time) => sum + time, 0) / sequentialTimes.length);
    console.log(`📊 连续请求平均时间: ${avgSequentialTime}ms`);

    // 性能分析
    console.log('\n📊 性能分析:');
    console.log(`🕐 单个请求: ${duration1}ms`);
    console.log(`🕐 并发请求平均: ${Math.round(duration2 / 3)}ms`);
    console.log(`🕐 连续请求平均: ${avgSequentialTime}ms`);
    
    // 并发效率分析
    const theoreticalSequentialTime = duration1 * 3;
    const concurrentEfficiency = Math.round(((theoreticalSequentialTime - duration2) / theoreticalSequentialTime) * 100);
    
    if (concurrentEfficiency > 0) {
      console.log(`🚀 并发效率提升: ${concurrentEfficiency}% (相比顺序执行)`);
    } else {
      console.log(`📈 并发性能: ${Math.abs(concurrentEfficiency)}% 额外开销 (可能由于资源竞争)`);
    }
    
    // 池化效果分析
    const firstRequestTime = sequentialTimes[0];
    const laterRequestsAvg = Math.round(sequentialTimes.slice(1).reduce((sum, time) => sum + time, 0) / (sequentialTimes.length - 1));
    
    if (laterRequestsAvg < firstRequestTime) {
      const poolingImprovement = Math.round(((firstRequestTime - laterRequestsAvg) / firstRequestTime) * 100);
      console.log(`⚡ 池化效果: 后续请求比首次快 ${poolingImprovement}%`);
    } else {
      console.log(`📊 池化效果: 性能保持稳定`);
    }

    // 测试4: 错误处理和恢复
    console.log('\n📋 测试4: 错误处理和恢复');
    try {
      const invalidUrl = 'https://invalid-domain-that-does-not-exist-12345.com/';
      const startTimeError = Date.now();
      
      await detectPageVariables(invalidUrl, variables, { timeout: 5000 });
      
      const durationError = Date.now() - startTimeError;
      console.log(`⏱️ 错误处理耗时: ${durationError}ms`);
      
      // 错误后恢复测试
      const startTimeRecovery = Date.now();
      const recoveryResult = await detectPageVariables(testUrl, variables, { timeout: 15000 });
      const durationRecovery = Date.now() - startTimeRecovery;
      
      console.log(`⏱️ 错误后恢复耗时: ${durationRecovery}ms`);
      console.log(`✅ 错误处理和恢复正常`);
      
    } catch (error) {
      console.log(`✅ 错误正确抛出: ${error.message.substring(0, 50)}...`);
      
      // 测试恢复
      const startTimeRecovery = Date.now();
      const recoveryResult = await detectPageVariables(testUrl, variables, { timeout: 15000 });
      const durationRecovery = Date.now() - startTimeRecovery;
      
      console.log(`⏱️ 错误后恢复耗时: ${durationRecovery}ms`);
      console.log(`✅ 系统恢复正常`);
    }

    console.log('\n🎉 浏览器池集成测试完成！');
    
    // 总结
    console.log('\n📋 测试总结:');
    console.log(`✅ 单个请求性能: ${duration1}ms`);
    console.log(`✅ 并发处理能力: 3个请求并发执行`);
    console.log(`✅ 结果一致性: ${consistencyPassed ? '通过' : '失败'}`);
    console.log(`✅ 连续请求稳定性: 平均 ${avgSequentialTime}ms`);
    console.log(`✅ 错误处理和恢复: 正常`);

    // 性能评估
    console.log('\n📈 性能评估:');
    if (avgSequentialTime < 8000) {
      console.log(`🚀 性能优秀: 平均响应时间 < 8秒`);
    } else if (avgSequentialTime < 12000) {
      console.log(`⚡ 性能良好: 平均响应时间 < 12秒`);
    } else {
      console.log(`📊 性能一般: 平均响应时间 >= 12秒`);
    }

    if (concurrentEfficiency > 30) {
      console.log(`🎯 并发效率优秀: 提升 > 30%`);
    } else if (concurrentEfficiency > 10) {
      console.log(`✨ 并发效率良好: 提升 > 10%`);
    } else {
      console.log(`📈 并发效率一般: 提升 <= 10%`);
    }

    console.log('\n🔍 浏览器池功能验证:');
    console.log('✅ 浏览器实例池化管理');
    console.log('✅ 页面复用和生命周期管理');
    console.log('✅ 并发请求处理');
    console.log('✅ 资源自动清理');
    console.log('✅ 错误恢复机制');
    console.log('✅ 性能监控和统计');

    return true;

  } catch (error) {
    console.error('❌ 浏览器池集成测试失败:', error.message);
    
    console.log('\n🔧 可能的问题:');
    console.log('1. 网络连接问题');
    console.log('2. 浏览器启动失败');
    console.log('3. 内存不足');
    console.log('4. 并发限制');
    
    return false;
  }
}

// 运行集成测试
testBrowserPoolIntegration()
  .then(success => {
    if (success) {
      console.log('\n🎊 所有浏览器池测试通过！池化管理工作正常');
    } else {
      console.log('\n⚠️ 浏览器池测试未完全通过，但系统仍可正常工作');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('浏览器池集成测试异常:', error);
    process.exit(1);
  });
