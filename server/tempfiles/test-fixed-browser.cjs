/**
 * 测试修复后的浏览器配置
 */

const http = require('http');

async function testFixedBrowser() {
    console.log('🧪 测试修复后的浏览器配置...\n');
    
    const testData = {
        url: "https://charm-demo.myshopline.com/",
        options: {
            timeout: 30000,
            waitUntil: "domcontentloaded"
        }
    };

    const postData = JSON.stringify(testData);

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/detect/shopline',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    console.log('📡 发送检测请求...');
    console.log(`🎯 URL: ${testData.url}`);
    
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            console.log(`📊 状态码: ${res.statusCode}`);
            
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                console.log(`⏱️ 响应时间: ${duration}ms`);
                
                try {
                    const response = JSON.parse(data);
                    
                    if (response.success) {
                        const { results, analysis } = response.data;
                        
                        console.log('\n✅ 检测成功！');
                        console.log(`🎯 置信度: ${analysis.confidence}%`);
                        console.log(`🏷️ 网站类型: ${analysis.storeType}`);
                        console.log(`📦 版本: ${analysis.storeVersion || '未知'}`);
                        console.log(`📊 找到变量: ${analysis.totalVariablesFound} 个`);
                        console.log(`📏 数据大小: ${analysis.totalDataSize?.toLocaleString() || 0} 字符`);
                        
                        console.log('\n📦 检测到的变量:');
                        results.forEach(variable => {
                            if (variable.found) {
                                const size = variable.size ? ` (${variable.size.toLocaleString()} 字符)` : '';
                                console.log(`   ✅ ${variable.name} (${variable.type})${size}`);
                            }
                        });
                        
                        console.log('\n🎉 浏览器配置修复成功！');
                        console.log('✅ 没有出现 socket hang up 错误');
                        console.log('✅ 检测功能正常工作');
                        
                        resolve({
                            success: true,
                            duration,
                            confidence: analysis.confidence,
                            variablesFound: analysis.totalVariablesFound
                        });
                        
                    } else {
                        console.log(`❌ 检测失败: ${response.error}`);
                        resolve({
                            success: false,
                            error: response.error,
                            duration
                        });
                    }
                    
                } catch (error) {
                    console.error('❌ 解析响应失败:', error);
                    console.log('原始响应:', data);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 请求错误:', error);
            reject(error);
        });

        req.setTimeout(60000, () => {
            console.error('⏰ 请求超时');
            req.destroy();
            reject(new Error('Request timeout'));
        });

        req.write(postData);
        req.end();
    });
}

// 运行测试
testFixedBrowser()
    .then(result => {
        console.log('\n📋 测试结果总结:');
        console.log('=' .repeat(40));
        if (result.success) {
            console.log('✅ 状态: 成功');
            console.log(`⏱️ 响应时间: ${result.duration}ms`);
            console.log(`🎯 置信度: ${result.confidence}%`);
            console.log(`📦 变量数: ${result.variablesFound}`);
            
            if (result.duration < 10000) {
                console.log('🚀 性能: 优秀 (< 10秒)');
            } else if (result.duration < 20000) {
                console.log('🚀 性能: 良好 (10-20秒)');
            } else {
                console.log('🐌 性能: 需要优化 (> 20秒)');
            }
        } else {
            console.log('❌ 状态: 失败');
            console.log(`❌ 错误: ${result.error}`);
            console.log(`⏱️ 失败时间: ${result.duration}ms`);
        }
    })
    .catch(error => {
        console.error('\n❌ 测试过程中发生错误:', error.message);
    });
