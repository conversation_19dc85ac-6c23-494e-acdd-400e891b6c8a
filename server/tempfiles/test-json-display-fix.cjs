/**
 * 测试 JSON 显示修复效果
 * 验证 Shopline 变量现在能完整显示而不是 [Large Object]
 */

const fetch = require('node-fetch');

async function testJsonDisplayFix() {
  console.log('🧪 测试 JSON 显示修复效果\n');

  const baseUrl = 'http://localhost:3000';
  
  try {
    console.log('📋 测试 Shopline 变量完整显示');
    
    const response = await fetch(`${baseUrl}/detect/shopline`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: 'https://charm-demo.myshopline.com/'
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      console.log('❌ API 请求失败:', data.error);
      return false;
    }

    const { results } = data.data;
    
    console.log('✅ API 请求成功');
    console.log('');
    
    // 检查 Shopline 变量
    const shoplineVar = results.find(r => r.name === 'Shopline');
    
    if (!shoplineVar) {
      console.log('❌ 未找到 Shopline 变量');
      return false;
    }
    
    console.log('📦 Shopline 变量分析:');
    console.log(`✅ 变量名: ${shoplineVar.name}`);
    console.log(`✅ 是否找到: ${shoplineVar.found}`);
    console.log(`✅ 数据类型: ${shoplineVar.type}`);
    console.log(`✅ 数据大小: ${shoplineVar.size.toLocaleString()} 字符`);
    console.log('');
    
    // 检查值的类型和内容
    if (shoplineVar.value === '[Large Object]') {
      console.log('❌ 仍然显示 [Large Object]，修复失败');
      return false;
    }
    
    if (typeof shoplineVar.value === 'string') {
      console.log('❌ 值是字符串类型，应该是对象类型');
      console.log(`   实际值: ${shoplineVar.value}`);
      return false;
    }
    
    if (typeof shoplineVar.value !== 'object' || shoplineVar.value === null) {
      console.log('❌ 值不是对象类型');
      console.log(`   实际类型: ${typeof shoplineVar.value}`);
      console.log(`   实际值: ${shoplineVar.value}`);
      return false;
    }
    
    console.log('✅ 值是完整的对象类型');
    
    // 检查对象的关键属性
    const expectedKeys = ['merchantId', 'storeId', 'currency', 'handle'];
    const foundKeys = [];
    const missingKeys = [];
    
    expectedKeys.forEach(key => {
      if (key in shoplineVar.value) {
        foundKeys.push(key);
      } else {
        missingKeys.push(key);
      }
    });
    
    console.log('🔍 关键属性检查:');
    foundKeys.forEach(key => {
      console.log(`✅ ${key}: ${shoplineVar.value[key]}`);
    });
    
    if (missingKeys.length > 0) {
      console.log('⚠️ 缺失的属性:');
      missingKeys.forEach(key => {
        console.log(`❌ ${key}`);
      });
    }
    
    // 计算对象的属性数量
    const propertyCount = Object.keys(shoplineVar.value).length;
    console.log(`📊 对象属性数量: ${propertyCount}`);
    
    // 检查 JSON 序列化
    try {
      const jsonString = JSON.stringify(shoplineVar.value, null, 2);
      const jsonSize = jsonString.length;
      
      console.log(`📏 JSON 序列化大小: ${jsonSize.toLocaleString()} 字符`);
      
      // 显示 JSON 的前几行作为示例
      const jsonLines = jsonString.split('\n');
      console.log('');
      console.log('📄 JSON 内容预览（前10行）:');
      jsonLines.slice(0, 10).forEach((line, index) => {
        console.log(`${(index + 1).toString().padStart(2, ' ')}: ${line}`);
      });
      
      if (jsonLines.length > 10) {
        console.log(`... (还有 ${jsonLines.length - 10} 行)`);
      }
      
    } catch (error) {
      console.log('❌ JSON 序列化失败:', error.message);
      return false;
    }
    
    console.log('');
    
    // 检查 __ENV__ 变量
    const envVar = results.find(r => r.name === '__ENV__');
    if (envVar && envVar.found) {
      console.log('📦 __ENV__ 变量分析:');
      console.log(`✅ 数据大小: ${envVar.size.toLocaleString()} 字符`);
      
      if (typeof envVar.value === 'object' && envVar.value !== null) {
        console.log(`✅ 对象属性数量: ${Object.keys(envVar.value).length}`);
        
        // 显示一些关键的环境变量
        const envKeys = ['APP_ENV', 'SENTRY_DSN', 'SDK_UMDJS'];
        envKeys.forEach(key => {
          if (key in envVar.value) {
            console.log(`✅ ${key}: ${envVar.value[key]}`);
          }
        });
      }
    }
    
    console.log('');
    console.log('🎉 JSON 显示修复验证结果:');
    console.log('✅ Shopline 变量返回完整对象数据');
    console.log('✅ 不再显示 [Large Object] 占位符');
    console.log('✅ 前端现在可以完整显示 JSON 内容');
    console.log('✅ 用户可以查看所有 Shopline 配置信息');
    
    return true;

  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message);
    return false;
  }
}

// 运行测试
testJsonDisplayFix()
  .then(success => {
    if (success) {
      console.log('\n🎊 JSON 显示修复验证通过！');
      console.log('🌐 用户现在可以在前端看到完整的 Shopline 变量内容');
    } else {
      console.log('\n⚠️ JSON 显示修复验证未通过，需要进一步检查');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
  });
