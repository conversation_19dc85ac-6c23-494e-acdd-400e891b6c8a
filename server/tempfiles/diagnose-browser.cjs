/**
 * 全面浏览器诊断脚本 v2.0
 * 深度分析 bottle-demo.myshopline.com 的浏览器断开连接问题
 * 包含详细的步骤日志、多种配置测试和根本原因分析
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function diagnoseBrowserIssues() {
  console.log('🔍 开始诊断浏览器问题...\n');

  const testUrl = 'https://faithdoodle.shoplineapp.com/';
  let browser = null;
  let page = null;

  try {
    console.log('🚀 启动浏览器...');
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-blink-features=AutomationControlled',
        '--memory-pressure-off',
        '--disable-background-networking',
        '--disable-default-apps',
        '--disable-extensions',
        '--disable-sync',
        '--disable-translate',
        '--hide-scrollbars',
        '--mute-audio',
        '--no-first-run',
        '--no-default-browser-check'
      ],
      defaultViewport: { width: 1366, height: 768 },
      ignoreDefaultArgs: ['--enable-automation'],
      timeout: 60000,
      protocolTimeout: 60000
    });

    console.log('✅ 浏览器启动成功');

    // 监听浏览器事件
    browser.on('disconnected', () => {
      console.log('❌ 浏览器断开连接！');
    });

    console.log('📄 创建新页面...');
    page = await browser.newPage();

    // 监听页面事件
    page.on('error', (error) => {
      console.log('❌ 页面错误:', error.message);
    });

    page.on('framedetached', (frame) => {
      console.log('🔗 框架分离:', frame.url());
    });

    console.log(`🌐 导航到: ${testUrl}`);
    const startTime = Date.now();

    try {
      await page.goto(testUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 15000
      });
      console.log('✅ 页面导航成功');

      // 等待页面稳定
      console.log('⏳ 等待页面稳定...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 检查页面状态
      console.log('🔍 检查页面状态...');
      const isConnected = browser.isConnected();
      const isClosed = page.isClosed();
      console.log(`浏览器连接状态: ${isConnected}`);
      console.log(`页面关闭状态: ${isClosed}`);

      if (isConnected && !isClosed) {
        // 尝试检测变量
        console.log('🔍 检测 window.Shopline 变量...');
        const shoplineExists = await page.evaluate(() => {
          return typeof window.Shopline !== 'undefined';
        });

        console.log(`window.Shopline 存在: ${shoplineExists}`);

        if (shoplineExists) {
          const shoplineValue = await page.evaluate(() => {
            return typeof window.Shopline;
          });
          console.log('✅ 成功获取 Shopline 变量类型:', shoplineValue);
        }

        // 检测其他变量
        const variables = ['mainConfig', 'dataLayer', 'gtag', 'fbq', '__SHOPLINE_CONFIG__'];
        for (const varName of variables) {
          try {
            const exists = await page.evaluate((name) => {
              return typeof window[name] !== 'undefined';
            }, varName);
            console.log(`window.${varName} 存在: ${exists}`);
          } catch (error) {
            console.log(`❌ 检测 ${varName} 时出错:`, error.message);
          }
        }

        console.log('✅ 检测完成');
      } else {
        console.log('❌ 页面或浏览器状态异常');
      }

    } catch (error) {
      console.log(`❌ 导航失败:`, error.message);
      console.log('错误类型:', error.constructor.name);
      
      if (error.message.includes('frame was detached')) {
        console.log('🔍 检测到框架分离错误');
      }
      if (error.message.includes('Protocol error')) {
        console.log('🔍 检测到协议错误');
      }
      if (error.message.includes('Browser disconnected')) {
        console.log('🔍 检测到浏览器断开连接');
      }
    }

    const endTime = Date.now();
    console.log(`\n⏱️ 总耗时: ${endTime - startTime}ms`);

  } catch (error) {
    console.error('💥 诊断过程中发生错误:', error);
  } finally {
    console.log('\n🧹 清理资源...');
    
    try {
      if (page && !page.isClosed()) {
        await page.close();
        console.log('✅ 页面已关闭');
      }
    } catch (error) {
      console.log('⚠️ 关闭页面时出错:', error.message);
    }

    try {
      if (browser && browser.isConnected()) {
        await browser.close();
        console.log('✅ 浏览器已关闭');
      }
    } catch (error) {
      console.log('⚠️ 关闭浏览器时出错:', error.message);
    }

    console.log('🎉 诊断完成');
  }
}

// 运行诊断
diagnoseBrowserIssues().catch(console.error);
