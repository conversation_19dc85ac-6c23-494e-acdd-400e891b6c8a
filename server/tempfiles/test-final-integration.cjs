/**
 * 最终集成测试 - 验证完整的业务逻辑实现
 */

const http = require('http');

async function testShoplineAPI(url, description) {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            url: url,
            options: {
                timeout: 30000,
                waitUntil: "domcontentloaded"
            }
        });

        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/detect/shopline',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    description,
                    status: res.statusCode,
                    data: JSON.parse(data)
                });
            });
        });

        req.on('error', reject);
        req.setTimeout(60000);
        req.write(postData);
        req.end();
    });
}

function validateBusinessLogic(result) {
    console.log(`\n📊 ${result.description}:`);
    console.log('=' .repeat(60));
    
    if (!result.data.success) {
        console.log(`❌ API 调用失败: ${result.data.error}`);
        return false;
    }
    
    const { results, analysis } = result.data.data;
    
    // 检查变量存在情况
    const hasMainConfig = results.some(r => r.name === 'mainConfig' && r.found);
    const hasShopline = results.some(r => r.name === 'Shopline' && r.found);
    const hasEnv = results.some(r => r.name === '__ENV__' && r.found);
    const storeVersionVar = results.find(r => r.name === 'storeVersion');
    
    console.log('🔍 检测到的关键变量:');
    console.log(`   mainConfig: ${hasMainConfig ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`   Shopline: ${hasShopline ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`   __ENV__: ${hasEnv ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`   storeVersion: ${storeVersionVar?.found ? `✅ ${storeVersionVar.value}` : '❌ 不存在'}`);
    
    console.log('\n📈 分析结果:');
    console.log(`   🏪 SHOPLINE 网站: ${analysis.isShoplineStore ? '是' : '否'}`);
    console.log(`   🏷️ 网站类型: ${analysis.storeType}`);
    console.log(`   📦 版本: ${analysis.storeVersion || '未知'}`);
    console.log(`   🎯 置信度: ${analysis.confidence}%`);
    console.log(`   📊 变量数量: ${analysis.totalVariablesFound}`);
    console.log(`   📏 数据大小: ${analysis.totalDataSize?.toLocaleString() || 0} 字符`);
    
    // 验证业务逻辑
    console.log('\n🧪 业务逻辑验证:');
    let logicValid = false;
    
    if (hasMainConfig && !hasShopline) {
        // 逻辑1: mainConfig 存在，Shopline 不存在
        const expectedVersion = 1.0;
        const actualVersion = storeVersionVar?.value;
        const versionCorrect = actualVersion === expectedVersion;
        const onlyMainConfigReturned = results.filter(r => r.found).length === 2 && 
                                     results.some(r => r.name === 'mainConfig' && r.found);
        
        console.log('   ✅ 符合逻辑1: mainConfig 存在，Shopline 不存在');
        console.log(`   📦 版本正确: ${versionCorrect ? '✅' : '❌'} (期望: ${expectedVersion}, 实际: ${actualVersion})`);
        console.log(`   📦 只返回 mainConfig: ${onlyMainConfigReturned ? '✅' : '❌'}`);
        
        logicValid = versionCorrect && onlyMainConfigReturned;
        
    } else if (!hasMainConfig && hasShopline) {
        // 逻辑2: mainConfig 不存在，Shopline 存在
        const expectedVersion = 2.0;
        const actualVersion = storeVersionVar?.value;
        const versionCorrect = actualVersion === expectedVersion;
        const correctVariablesReturned = results.some(r => r.name === 'Shopline' && r.found) &&
                                       results.some(r => r.name === 'storeVersion' && r.found);
        
        console.log('   ✅ 符合逻辑2: mainConfig 不存在，Shopline 存在');
        console.log(`   📦 版本正确: ${versionCorrect ? '✅' : '❌'} (期望: ${expectedVersion}, 实际: ${actualVersion})`);
        console.log(`   📦 返回 Shopline + __ENV__: ${correctVariablesReturned ? '✅' : '❌'}`);
        
        logicValid = versionCorrect && correctVariablesReturned;
        
    } else {
        // 其他情况
        console.log('   ℹ️ 不符合特定逻辑，使用默认处理');
        console.log(`   📊 mainConfig: ${hasMainConfig ? '存在' : '不存在'}`);
        console.log(`   📊 Shopline: ${hasShopline ? '存在' : '不存在'}`);
        logicValid = true; // 默认情况认为是正确的
    }
    
    // 显示返回的所有变量
    console.log('\n📦 返回的变量列表:');
    results.forEach(variable => {
        if (variable.found) {
            const size = variable.size ? ` (${variable.size.toLocaleString()} 字符)` : '';
            console.log(`   ✅ ${variable.name} (${variable.type})${size}`);
            if (variable.name === 'storeVersion') {
                console.log(`       🎯 版本值: ${variable.value}`);
            }
        }
    });
    
    return logicValid;
}

async function runFinalIntegrationTest() {
    console.log('🧪 最终集成测试 - 验证完整业务逻辑实现\n');
    
    console.log('🎯 测试目标:');
    console.log('   1. 验证 API 端点正常工作');
    console.log('   2. 验证业务逻辑正确实现');
    console.log('   3. 验证返回数据格式正确');
    console.log('   4. 验证前端能正确显示结果\n');
    
    try {
        // 测试真实的 SHOPLINE 网站
        const testResult = await testShoplineAPI(
            'https://charm-demo.myshopline.com/',
            'SHOPLINE 演示网站 - 真实环境测试'
        );
        
        const isValid = validateBusinessLogic(testResult);
        
        console.log('\n📋 最终测试结果:');
        console.log('=' .repeat(60));
        console.log(`✅ API 连接: ${testResult.status === 200 ? '正常' : '异常'}`);
        console.log(`✅ 数据返回: ${testResult.data.success ? '正常' : '异常'}`);
        console.log(`✅ 业务逻辑: ${isValid ? '正确' : '错误'}`);
        
        if (testResult.status === 200 && testResult.data.success && isValid) {
            console.log('\n🎉 所有测试通过！');
            console.log('✅ 业务逻辑已正确实现:');
            console.log('   • mainConfig 存在，Shopline 不存在 → 返回 mainConfig + 版本 1.0');
            console.log('   • mainConfig 不存在，Shopline 存在 → 返回 Shopline + __ENV__ + 版本 2.0');
            console.log('   • 其他情况 → 返回所有检测到的变量');
            
            console.log('\n🌐 前端测试建议:');
            console.log('   1. 访问 http://localhost:5174/');
            console.log('   2. 输入 URL: https://charm-demo.myshopline.com/');
            console.log('   3. 点击检测按钮');
            console.log('   4. 查看是否显示版本信息和正确的变量');
            
        } else {
            console.log('\n❌ 测试失败，需要进一步调试');
            if (testResult.status !== 200) {
                console.log(`   • API 连接问题: HTTP ${testResult.status}`);
            }
            if (!testResult.data.success) {
                console.log(`   • API 错误: ${testResult.data.error}`);
            }
            if (!isValid) {
                console.log(`   • 业务逻辑问题: 返回结果不符合预期`);
            }
        }
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
    }
}

runFinalIntegrationTest();
