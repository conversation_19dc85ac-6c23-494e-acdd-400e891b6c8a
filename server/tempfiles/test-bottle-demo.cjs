/**
 * 专门测试 bottle-demo.myshopline.com 的脚本
 * 使用与诊断成功相同的配置
 */

const puppeteer = require('puppeteer');

async function testBottleDemo() {
  console.log('🧪 专门测试 bottle-demo.myshopline.com...\n');

  const testUrl = 'https://bottle-demo.myshopline.com/';
  let browser = null;
  let page = null;

  try {
    console.log('🚀 启动浏览器（最小配置）...');
    
    // 使用诊断验证成功的最小配置
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox'],
      timeout: 30000,
      protocolTimeout: 30000
    });

    console.log('✅ 浏览器启动成功');

    // 监听浏览器事件
    browser.on('disconnected', () => {
      console.log('❌ 浏览器断开连接事件');
    });

    console.log('📄 创建页面...');
    page = await browser.newPage();

    // 监听页面事件
    let frameDetachedCount = 0;
    let pageErrorCount = 0;

    page.on('error', (error) => {
      pageErrorCount++;
      console.log('❌ 页面错误:', error.message);
    });

    page.on('framedetached', (frame) => {
      frameDetachedCount++;
      console.log('🔗 框架分离:', frame.url());
    });

    console.log(`🌐 导航到: ${testUrl}`);
    const startTime = Date.now();

    await page.goto(testUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 15000
    });

    const loadTime = Date.now() - startTime;
    console.log(`✅ 页面导航成功，耗时: ${loadTime}ms`);

    // 检查浏览器状态
    const isConnected = browser.isConnected();
    const isClosed = page.isClosed();
    console.log(`🔍 状态检查: 浏览器连接=${isConnected}, 页面关闭=${isClosed}`);

    if (!isConnected || isClosed) {
      throw new Error('浏览器或页面状态异常');
    }

    // 额外等待确保页面完全加载
    console.log('⏳ 等待页面完全加载（4秒）...');
    await new Promise(resolve => setTimeout(resolve, 4000));

    // 再次检查状态
    const isConnected2 = browser.isConnected();
    const isClosed2 = page.isClosed();
    console.log(`🔍 等待后状态: 浏览器连接=${isConnected2}, 页面关闭=${isClosed2}`);

    if (!isConnected2 || isClosed2) {
      throw new Error('等待后浏览器或页面状态异常');
    }

    // 检测 Shopline 变量
    console.log('🔍 检测 window.Shopline...');
    
    const shoplineExists = await page.evaluate(() => {
      return typeof window.Shopline !== 'undefined';
    });

    console.log(`📊 window.Shopline 存在: ${shoplineExists}`);

    if (shoplineExists) {
      const shoplineType = await page.evaluate(() => {
        return typeof window.Shopline;
      });

      const shoplineKeys = await page.evaluate(() => {
        if (typeof window.Shopline === 'object' && window.Shopline !== null) {
          return Object.keys(window.Shopline);
        }
        return [];
      });

      console.log(`✅ 成功检测到 Shopline!`);
      console.log(`   类型: ${shoplineType}`);
      console.log(`   属性数量: ${shoplineKeys.length}`);
      console.log(`   主要属性: ${shoplineKeys.slice(0, 5).join(', ')}`);
    } else {
      console.log('❌ 未检测到 window.Shopline');
      
      // 尝试检测其他可能的变量
      const otherVars = ['mainConfig', 'dataLayer', 'gtag', 'fbq', '__SHOPLINE_CONFIG__'];
      console.log('🔍 检测其他变量...');
      
      for (const varName of otherVars) {
        try {
          const exists = await page.evaluate((name) => {
            return typeof window[name] !== 'undefined';
          }, varName);
          console.log(`   window.${varName}: ${exists ? '存在' : '不存在'}`);
        } catch (e) {
          console.log(`   window.${varName}: 检测失败`);
        }
      }
    }

    console.log(`\n📈 最终统计:`);
    console.log(`   框架分离次数: ${frameDetachedCount}`);
    console.log(`   页面错误次数: ${pageErrorCount}`);
    console.log(`   浏览器连接状态: ${browser.isConnected()}`);
    console.log(`   页面关闭状态: ${page.isClosed()}`);

    console.log('\n🎉 测试完成 - 成功！');

  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    console.error('错误类型:', error.constructor.name);
    
    if (error.message.includes('frame was detached')) {
      console.error('🔍 这是框架分离错误');
    }
    if (error.message.includes('Protocol error')) {
      console.error('🔍 这是协议错误');
    }
    if (error.message.includes('Browser disconnected')) {
      console.error('🔍 这是浏览器断开连接错误');
    }
  } finally {
    console.log('\n🧹 清理资源...');
    
    try {
      if (page && !page.isClosed()) {
        await page.close();
        console.log('✅ 页面已关闭');
      }
    } catch (error) {
      console.log('⚠️ 页面关闭失败:', error.message);
    }

    try {
      if (browser && browser.isConnected()) {
        await browser.close();
        console.log('✅ 浏览器已关闭');
      }
    } catch (error) {
      console.log('⚠️ 浏览器关闭失败:', error.message);
    }

    console.log('🎯 测试结束');
  }
}

// 运行测试
testBottleDemo().catch(console.error);
