/**
 * Shopline 变量数据完整性调查
 * 分析检测到的数据与真实浏览器中数据的差异
 */

const { chromium } = require('playwright');

async function investigateShoplineData() {
  console.log('🔍 Shopline 变量数据完整性调查\n');

  const testUrl = 'https://bottle-demo.myshopline.com/';
  let browser = null;
  let context = null;
  let page = null;

  try {
    console.log('🚀 启动浏览器...');
    browser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox'],
      timeout: 30000
    });

    context = await browser.newContext({
      viewport: { width: 1366, height: 768 },
      ignoreHTTPSErrors: true,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    });

    page = await context.newPage();

    console.log(`🌐 导航到: ${testUrl}`);
    await page.goto(testUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 15000
    });

    console.log('✅ 页面加载完成\n');

    // 在不同时间点检测 Shopline 变量
    const timingTests = [
      { name: '立即检测', delay: 0 },
      { name: '1秒后', delay: 1000 },
      { name: '3秒后', delay: 3000 },
      { name: '5秒后', delay: 5000 },
      { name: '8秒后', delay: 8000 },
      { name: '10秒后', delay: 10000 }
    ];

    const results = [];

    for (const timing of timingTests) {
      console.log(`⏰ ${timing.name}检测...`);
      
      if (timing.delay > 0) {
        await new Promise(resolve => setTimeout(resolve, timing.delay));
      }

      // 检测 Shopline 变量的存在和基本信息
      const basicInfo = await page.evaluate(() => {
        if (typeof window.Shopline === 'undefined') {
          return { exists: false };
        }

        const shopline = window.Shopline;
        return {
          exists: true,
          type: typeof shopline,
          isNull: shopline === null,
          constructor: shopline && shopline.constructor ? shopline.constructor.name : 'unknown',
          keysCount: shopline && typeof shopline === 'object' ? Object.keys(shopline).length : 0,
          keys: shopline && typeof shopline === 'object' ? Object.keys(shopline) : []
        };
      });

      if (!basicInfo.exists) {
        console.log('   ❌ window.Shopline 不存在');
        results.push({ timing: timing.name, exists: false });
        continue;
      }

      console.log(`   ✅ 存在: ${basicInfo.type}, 键数量: ${basicInfo.keysCount}`);
      console.log(`   🔑 键列表: ${basicInfo.keys.slice(0, 10).join(', ')}${basicInfo.keys.length > 10 ? '...' : ''}`);

      // 获取详细的变量内容（分步骤避免循环引用）
      const detailedData = await page.evaluate(() => {
        const shopline = window.Shopline;
        if (!shopline || typeof shopline !== 'object') {
          return null;
        }

        // 安全地提取数据
        const safeExtract = (obj, maxDepth = 3, currentDepth = 0) => {
          if (currentDepth >= maxDepth) {
            return '[Max Depth Reached]';
          }

          if (obj === null || obj === undefined) {
            return obj;
          }

          if (typeof obj !== 'object') {
            return obj;
          }

          if (Array.isArray(obj)) {
            return obj.slice(0, 5).map(item => safeExtract(item, maxDepth, currentDepth + 1));
          }

          const result = {};
          const keys = Object.keys(obj);
          
          // 限制键的数量以避免过大的对象
          for (let i = 0; i < Math.min(keys.length, 50); i++) {
            const key = keys[i];
            try {
              const value = obj[key];
              
              // 跳过函数和复杂对象
              if (typeof value === 'function') {
                result[key] = '[Function]';
              } else if (value && typeof value === 'object') {
                // 检查是否是 DOM 元素或其他复杂对象
                if (value.nodeType || value.constructor.name.includes('Element')) {
                  result[key] = '[DOM Element]';
                } else if (value.constructor.name === 'Object' || Array.isArray(value)) {
                  result[key] = safeExtract(value, maxDepth, currentDepth + 1);
                } else {
                  result[key] = `[${value.constructor.name}]`;
                }
              } else {
                result[key] = value;
              }
            } catch (e) {
              result[key] = '[Error accessing property]';
            }
          }

          return result;
        };

        return safeExtract(shopline);
      });

      // 计算数据大小
      let dataSize = 0;
      try {
        dataSize = JSON.stringify(detailedData).length;
      } catch (e) {
        dataSize = 0;
      }

      console.log(`   📊 数据大小: ${dataSize} 字符`);

      // 检查特定的重要属性
      const importantProps = await page.evaluate(() => {
        const shopline = window.Shopline;
        if (!shopline) return {};

        return {
          storeId: shopline.storeId,
          currency: shopline.currency,
          locale: shopline.locale,
          handle: shopline.handle,
          customerCountry: shopline.customerCountry,
          currencyConfig: shopline.currencyConfig ? 'exists' : 'missing',
          themeId: shopline.themeId,
          themeName: shopline.themeName,
          themeVersion: shopline.themeVersion,
          // 检查是否有更多属性在后续加载
          hasMoreProps: Object.keys(shopline).length > 10
        };
      });

      console.log(`   🎯 重要属性:`, importantProps);

      results.push({
        timing: timing.name,
        delay: timing.delay,
        exists: true,
        keysCount: basicInfo.keysCount,
        keys: basicInfo.keys,
        dataSize: dataSize,
        importantProps: importantProps,
        detailedData: detailedData
      });

      console.log(''); // 空行分隔
    }

    // 分析结果
    console.log('📈 分析结果:\n');

    // 检查数据是否随时间增长
    const existingResults = results.filter(r => r.exists);
    if (existingResults.length > 1) {
      console.log('🔍 数据变化分析:');
      
      for (let i = 1; i < existingResults.length; i++) {
        const prev = existingResults[i - 1];
        const curr = existingResults[i];
        
        const keysChange = curr.keysCount - prev.keysCount;
        const sizeChange = curr.dataSize - prev.dataSize;
        
        console.log(`   ${prev.timing} → ${curr.timing}:`);
        console.log(`     键数量变化: ${keysChange > 0 ? '+' : ''}${keysChange}`);
        console.log(`     数据大小变化: ${sizeChange > 0 ? '+' : ''}${sizeChange} 字符`);
        
        // 检查新增的键
        const newKeys = curr.keys.filter(key => !prev.keys.includes(key));
        if (newKeys.length > 0) {
          console.log(`     新增键: ${newKeys.join(', ')}`);
        }
      }
    }

    // 找出最完整的数据
    const mostComplete = existingResults.reduce((max, curr) => 
      curr.keysCount > max.keysCount ? curr : max, existingResults[0]);

    console.log(`\n🏆 最完整的数据在: ${mostComplete.timing}`);
    console.log(`   键数量: ${mostComplete.keysCount}`);
    console.log(`   数据大小: ${mostComplete.dataSize} 字符`);
    console.log(`   所有键: ${mostComplete.keys.join(', ')}`);

    // 输出最完整的数据结构
    console.log('\n📋 最完整的 Shopline 对象结构:');
    console.log(JSON.stringify(mostComplete.detailedData, null, 2));

    // 与当前检测方法对比
    console.log('\n🔄 与当前检测方法对比:');
    console.log('当前方法问题可能包括:');
    console.log('1. 检测时机过早，变量尚未完全填充');
    console.log('2. 循环引用处理过于简化，丢失了重要数据');
    console.log('3. 序列化限制导致数据截断');
    
    if (mostComplete.delay > 4000) {
      console.log('⚠️ 建议: 增加检测延迟到至少 5-8 秒');
    }

  } catch (error) {
    console.error('💥 调查失败:', error.message);
  } finally {
    console.log('\n🧹 清理资源...');
    
    try {
      if (page) await page.close();
      if (context) await context.close();
      if (browser) await browser.close();
    } catch (e) {
      console.log('清理时出错:', e.message);
    }

    console.log('🎯 调查完成');
  }
}

// 运行调查
investigateShoplineData().catch(console.error);
