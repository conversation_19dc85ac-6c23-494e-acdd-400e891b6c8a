/**
 * 测试 Playwright 版本的检测脚本
 * 验证 Playwright 替换 Puppeteer 后的性能和稳定性
 */

const { chromium } = require('playwright');

async function testPlaywrightDetection() {
  console.log('🎭 测试 Playwright 版本的检测...\n');

  const testUrl = 'https://bottle-demo.myshopline.com/';
  let browser = null;
  let context = null;
  let page = null;

  try {
    console.log('🚀 启动 Playwright 浏览器...');
    const startTime = Date.now();
    
    browser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox'],
      timeout: 30000
    });

    const launchTime = Date.now() - startTime;
    console.log(`✅ 浏览器启动成功，耗时: ${launchTime}ms`);

    // 创建浏览器上下文
    console.log('📄 创建浏览器上下文...');
    context = await browser.newContext({
      viewport: { width: 1366, height: 768 },
      ignoreHTTPSErrors: true,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    });

    console.log('📄 创建页面...');
    page = await context.newPage();

    // 监听页面事件
    let frameDetachedCount = 0;
    let pageErrorCount = 0;

    page.on('crash', () => {
      console.log('💥 页面崩溃');
    });

    page.on('close', () => {
      console.log('🔒 页面关闭');
    });

    // 设置反检测脚本
    await page.addInitScript(`
      // 移除 webdriver 属性
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      // 伪造基础属性
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });

      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });
    `);

    // 设置请求拦截
    await page.route('**/*', (route) => {
      const request = route.request();
      const resourceType = request.resourceType();

      // 只阻止图片以节省带宽
      if (resourceType === 'image') {
        route.abort();
        return;
      }

      route.continue();
    });

    console.log(`🌐 导航到: ${testUrl}`);
    const navStartTime = Date.now();

    await page.goto(testUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 15000
    });

    const navTime = Date.now() - navStartTime;
    console.log(`✅ 页面导航成功，耗时: ${navTime}ms`);

    // 等待页面完全加载
    console.log('⏳ 等待页面完全加载（4秒）...');
    await new Promise(resolve => setTimeout(resolve, 4000));

    // 检测 Shopline 变量
    console.log('🔍 检测 window.Shopline...');
    
    const shoplineExists = await page.evaluate(() => {
      return typeof window.Shopline !== 'undefined';
    });

    console.log(`📊 window.Shopline 存在: ${shoplineExists}`);

    if (shoplineExists) {
      const shoplineType = await page.evaluate(() => {
        return typeof window.Shopline;
      });

      const shoplineKeys = await page.evaluate(() => {
        if (typeof window.Shopline === 'object' && window.Shopline !== null) {
          return Object.keys(window.Shopline);
        }
        return [];
      });

      console.log(`✅ 成功检测到 Shopline!`);
      console.log(`   类型: ${shoplineType}`);
      console.log(`   属性数量: ${shoplineKeys.length}`);
      console.log(`   主要属性: ${shoplineKeys.slice(0, 5).join(', ')}`);

      // 获取一些关键属性
      const storeInfo = await page.evaluate(() => {
        if (typeof window.Shopline === 'object' && window.Shopline !== null) {
          return {
            storeId: window.Shopline.storeId,
            currency: window.Shopline.currency,
            locale: window.Shopline.locale
          };
        }
        return null;
      });

      if (storeInfo) {
        console.log(`   商店信息:`, storeInfo);
      }
    } else {
      console.log('❌ 未检测到 window.Shopline');
    }

    // 检测其他变量
    const otherVars = ['mainConfig', 'dataLayer', 'gtag', 'fbq', '__SHOPLINE_CONFIG__'];
    console.log('\n🔍 检测其他变量...');
    
    for (const varName of otherVars) {
      try {
        const exists = await page.evaluate((name) => {
          return typeof window[name] !== 'undefined';
        }, varName);
        console.log(`   window.${varName}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
      } catch (e) {
        console.log(`   window.${varName}: ❌ 检测失败`);
      }
    }

    const totalTime = Date.now() - startTime;
    console.log(`\n📈 性能统计:`);
    console.log(`   总耗时: ${totalTime}ms`);
    console.log(`   启动时间: ${launchTime}ms`);
    console.log(`   导航时间: ${navTime}ms`);
    console.log(`   页面错误: ${pageErrorCount}`);
    console.log(`   框架分离: ${frameDetachedCount}`);

    console.log('\n🎉 Playwright 测试完成 - 成功！');

  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    console.error('错误类型:', error.constructor.name);
  } finally {
    console.log('\n🧹 清理资源...');
    
    try {
      if (page) {
        await page.close();
        console.log('✅ 页面已关闭');
      }
    } catch (error) {
      console.log('⚠️ 页面关闭失败:', error.message);
    }

    try {
      if (context) {
        await context.close();
        console.log('✅ 上下文已关闭');
      }
    } catch (error) {
      console.log('⚠️ 上下文关闭失败:', error.message);
    }

    try {
      if (browser) {
        await browser.close();
        console.log('✅ 浏览器已关闭');
      }
    } catch (error) {
      console.log('⚠️ 浏览器关闭失败:', error.message);
    }

    console.log('🎯 测试结束');
  }
}

// 运行测试
testPlaywrightDetection().catch(console.error);
