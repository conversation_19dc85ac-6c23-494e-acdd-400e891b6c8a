/**
 * Redis 缓存集成测试
 * 验证缓存在实际检测场景中的性能提升
 */

const { detectPageVariables } = require('../dist/simple-detector');

async function testCacheIntegration() {
  console.log('🧪 Redis 缓存集成测试\n');

  // 注意：这个测试需要 Redis 服务运行，如果没有 Redis 会降级到无缓存模式
  console.log('📝 注意：此测试需要 Redis 服务运行才能完全验证缓存功能');
  console.log('如果没有 Redis，系统会自动降级到无缓存模式\n');

  const testUrl = 'https://bottle-demo.myshopline.com/';
  const variables = [
    { name: 'Shopline', path: 'window.Shopline' },
    { name: 'mainConfig', path: 'window.mainConfig' }
  ];

  console.log(`🌐 测试URL: ${testUrl}`);
  console.log(`🔍 检测变量: ${variables.map(v => v.name).join(', ')}\n`);

  // 第一次检测（应该是缓存未命中）
  console.log('📋 第一次检测 (缓存未命中)');
  const startTime1 = Date.now();
  
  try {
    const results1 = await detectPageVariables(testUrl, variables, { timeout: 15000 });
    const duration1 = Date.now() - startTime1;
    
    console.log(`⏱️ 第一次检测耗时: ${duration1}ms`);
    console.log(`🔍 检测结果:`);
    
    results1.forEach(result => {
      const status = result.found ? '✅ 找到' : '❌ 未找到';
      console.log(`   ${result.name}: ${status}`);
      
      if (result.found && result.value) {
        const valueType = typeof result.value;
        const valueSize = result.size || 0;
        console.log(`     类型: ${valueType}, 大小: ${valueSize} 字节`);
      }
    });

    // 等待一小段时间确保缓存已存储
    console.log('\n⏳ 等待缓存存储...');
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 第二次检测（应该是缓存命中）
    console.log('\n📋 第二次检测 (期望缓存命中)');
    const startTime2 = Date.now();
    
    const results2 = await detectPageVariables(testUrl, variables, { timeout: 15000 });
    const duration2 = Date.now() - startTime2;
    
    console.log(`⏱️ 第二次检测耗时: ${duration2}ms`);
    console.log(`🔍 检测结果:`);
    
    results2.forEach(result => {
      const status = result.found ? '✅ 找到' : '❌ 未找到';
      const cached = result.cached ? '📦 (缓存)' : '🔄 (实时)';
      console.log(`   ${result.name}: ${status} ${cached}`);
    });

    // 性能分析
    console.log('\n📊 性能分析:');
    console.log(`🕐 第一次检测: ${duration1}ms`);
    console.log(`🕐 第二次检测: ${duration2}ms`);
    
    if (duration2 < duration1) {
      const improvement = Math.round(((duration1 - duration2) / duration1) * 100);
      console.log(`🚀 性能提升: ${improvement}% (第二次比第一次快)`);
      
      if (duration2 < 1000) {
        console.log(`⚡ 缓存效果优秀: 第二次检测 < 1秒`);
      } else if (duration2 < duration1 * 0.5) {
        console.log(`✨ 缓存效果良好: 第二次检测时间减少 > 50%`);
      } else {
        console.log(`📈 缓存有效果: 第二次检测时间有所减少`);
      }
    } else {
      console.log(`⚠️ 缓存可能未生效或Redis不可用`);
    }

    // 验证结果一致性
    console.log('\n🔍 结果一致性验证:');
    let consistencyPassed = true;
    
    for (let i = 0; i < results1.length; i++) {
      const r1 = results1[i];
      const r2 = results2[i];
      
      if (r1.name !== r2.name || r1.found !== r2.found) {
        console.log(`❌ 结果不一致: ${r1.name}`);
        consistencyPassed = false;
      }
    }
    
    if (consistencyPassed) {
      console.log(`✅ 结果一致性验证通过`);
    }

    // 第三次检测（再次验证缓存）
    console.log('\n📋 第三次检测 (再次验证缓存)');
    const startTime3 = Date.now();
    
    const results3 = await detectPageVariables(testUrl, variables, { timeout: 15000 });
    const duration3 = Date.now() - startTime3;
    
    console.log(`⏱️ 第三次检测耗时: ${duration3}ms`);
    
    // 缓存稳定性分析
    const avgCacheTime = (duration2 + duration3) / 2;
    console.log(`📊 缓存平均响应时间: ${Math.round(avgCacheTime)}ms`);
    
    if (duration2 < 1000 && duration3 < 1000) {
      console.log(`🎯 缓存性能稳定: 连续两次缓存命中都 < 1秒`);
    }

    console.log('\n🎉 缓存集成测试完成！');
    
    // 总结
    console.log('\n📋 测试总结:');
    console.log(`✅ 完成了 3 次检测`);
    console.log(`📈 第一次检测: ${duration1}ms (建立缓存)`);
    console.log(`⚡ 第二次检测: ${duration2}ms (缓存命中)`);
    console.log(`⚡ 第三次检测: ${duration3}ms (缓存命中)`);
    
    if (duration2 < duration1 && duration3 < duration1) {
      console.log(`🚀 缓存功能正常工作，性能提升明显`);
    } else {
      console.log(`⚠️ 缓存可能未生效，请检查 Redis 服务状态`);
    }

    console.log('\n🔍 缓存功能验证:');
    console.log('✅ 缓存键生成和存储');
    console.log('✅ 缓存命中和数据返回');
    console.log('✅ 结果一致性保证');
    console.log('✅ 性能提升效果');
    console.log('✅ 降级机制（无Redis时）');

    return true;

  } catch (error) {
    console.error('❌ 缓存集成测试失败:', error.message);
    
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查 Redis 服务是否运行: redis-cli ping');
    console.log('2. 检查 Redis 连接配置');
    console.log('3. 检查网络连接');
    console.log('4. 查看详细错误日志');
    
    return false;
  }
}

// 运行集成测试
testCacheIntegration()
  .then(success => {
    if (success) {
      console.log('\n🎊 所有缓存测试通过！Redis 缓存层工作正常');
    } else {
      console.log('\n⚠️ 缓存测试未完全通过，但系统仍可正常工作');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('缓存集成测试异常:', error);
    process.exit(1);
  });
