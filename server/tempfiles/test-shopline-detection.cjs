/**
 * 测试 SHOPLINE 专用检测端点
 */

const http = require('http');

async function testShoplineDetection() {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            url: "https://charm-demo.myshopline.com/",
            options: {
                timeout: 30000,
                waitUntil: "domcontentloaded"
            }
        });

        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/detect/shopline',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    status: res.statusCode,
                    headers: res.headers,
                    data: JSON.parse(data)
                });
            });
        });

        req.on('error', reject);
        req.setTimeout(60000);
        req.write(postData);
        req.end();
    });
}

async function runTest() {
    console.log('🧪 测试 SHOPLINE 专用检测端点...\n');

    try {
        console.log('🔍 正在检测 https://charm-demo.myshopline.com/...');
        const result = await testShoplineDetection();
        
        console.log(`✅ 状态码: ${result.status}`);
        
        if (result.data.success) {
            const { results, analysis } = result.data.data;
            
            console.log('\n📊 检测分析:');
            console.log(`   🏪 是否为 SHOPLINE 网站: ${analysis.isShoplineStore ? '是' : '否'}`);
            console.log(`   🎯 置信度: ${analysis.confidence}%`);
            console.log(`   📦 找到变量数量: ${analysis.totalVariablesFound}`);
            console.log(`   📏 总数据大小: ${analysis.totalDataSize} 字符`);
            console.log(`   ⚙️ 有环境配置: ${analysis.hasEnvConfig ? '是' : '否'}`);
            console.log(`   🔧 有 SHOPLINE SDK: ${analysis.hasShoplineSDK ? '是' : '否'}`);
            
            if (analysis.hasAnalytics) {
                console.log('\n📈 分析工具检测:');
                console.log(`   📊 Google Analytics: ${analysis.hasAnalytics.googleAnalytics ? '是' : '否'}`);
                console.log(`   📘 Facebook Pixel: ${analysis.hasAnalytics.facebookPixel ? '是' : '否'}`);
                console.log(`   📋 Data Layer: ${analysis.hasAnalytics.dataLayer ? '是' : '否'}`);
            }
            
            console.log('\n🔍 检测到的变量:');
            results.forEach(variable => {
                const status = variable.found ? '✅' : '❌';
                const size = variable.size ? ` (${variable.size} 字符)` : '';
                console.log(`   ${status} ${variable.name} (${variable.type})${size}`);
            });
            
            // 显示主要变量的部分内容
            const envVariable = results.find(r => r.name === '__ENV__' && r.found);
            if (envVariable && envVariable.value) {
                console.log('\n📦 __ENV__ 变量内容预览:');
                const preview = JSON.stringify(envVariable.value, null, 2).split('\n').slice(0, 10).join('\n');
                console.log(preview + '\n...(省略更多内容)');
            }
            
        } else {
            console.log(`❌ 检测失败: ${result.data.error}`);
        }
        
        console.log('\n✅ 测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

runTest();
