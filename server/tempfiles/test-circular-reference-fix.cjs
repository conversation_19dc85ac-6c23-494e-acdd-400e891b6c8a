/**
 * 测试循环引用修复效果
 * 验证 bottle-demo 网站的 Shopline 变量现在能正确显示而不是 [Circular Object]
 */

const fetch = require('node-fetch');

async function testCircularReferenceFix() {
  console.log('🧪 测试循环引用修复效果\n');

  const baseUrl = 'http://localhost:3000';
  
  try {
    console.log('📋 测试 bottle-demo 网站的 Shopline 变量');
    
    const response = await fetch(`${baseUrl}/detect/shopline`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: 'https://bottle-demo.myshopline.com/'
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      console.log('❌ API 请求失败:', data.error);
      return false;
    }

    const { results, analysis } = data.data;
    
    console.log('✅ API 请求成功');
    console.log('');
    
    // 检查 Shopline 变量
    const shoplineVar = results.find(r => r.name === 'Shopline');
    
    if (!shoplineVar) {
      console.log('❌ 未找到 Shopline 变量');
      return false;
    }
    
    console.log('📦 Shopline 变量分析:');
    console.log(`✅ 变量名: ${shoplineVar.name}`);
    console.log(`✅ 是否找到: ${shoplineVar.found}`);
    console.log(`✅ 数据类型: ${shoplineVar.type}`);
    console.log(`✅ 数据大小: ${shoplineVar.size.toLocaleString()} 字符`);
    console.log('');
    
    // 检查是否还是循环引用错误
    if (shoplineVar.value === '[Circular Object]') {
      console.log('❌ 仍然显示 [Circular Object]，修复失败');
      return false;
    }
    
    if (typeof shoplineVar.value === 'string' && shoplineVar.value.includes('Circular')) {
      console.log('❌ 仍然包含循环引用错误信息');
      console.log(`   实际值: ${shoplineVar.value}`);
      return false;
    }
    
    if (typeof shoplineVar.value !== 'object' || shoplineVar.value === null) {
      console.log('❌ 值不是对象类型');
      console.log(`   实际类型: ${typeof shoplineVar.value}`);
      return false;
    }
    
    console.log('✅ 值是完整的对象类型');
    
    // 检查对象的关键属性
    const expectedKeys = ['storeId', 'currency', 'locale', 'handle'];
    const foundKeys = [];
    const missingKeys = [];
    
    expectedKeys.forEach(key => {
      if (key in shoplineVar.value) {
        foundKeys.push(key);
      } else {
        missingKeys.push(key);
      }
    });
    
    console.log('🔍 关键属性检查:');
    foundKeys.forEach(key => {
      console.log(`✅ ${key}: ${shoplineVar.value[key]}`);
    });
    
    if (missingKeys.length > 0) {
      console.log('⚠️ 缺失的属性:');
      missingKeys.forEach(key => {
        console.log(`❌ ${key}`);
      });
    }
    
    // 计算对象的属性数量
    const propertyCount = Object.keys(shoplineVar.value).length;
    console.log(`📊 对象属性数量: ${propertyCount}`);
    
    // 检查是否包含循环引用标记
    const jsonString = JSON.stringify(shoplineVar.value, null, 2);
    const circularRefCount = (jsonString.match(/\[Circular Reference\]/g) || []).length;
    
    if (circularRefCount > 0) {
      console.log(`🔄 检测到 ${circularRefCount} 个循环引用标记（已正确处理）`);
    } else {
      console.log('✅ 无循环引用问题');
    }
    
    console.log('');
    
    // 检查分析结果
    console.log('🎯 检测分析结果:');
    console.log(`✅ 是否为 SHOPLINE 网站: ${analysis.isShoplineStore}`);
    console.log(`✅ 置信度: ${analysis.confidence}%`);
    console.log(`✅ 商店类型: ${analysis.storeType}`);
    
    if (analysis.storeVersion) {
      console.log(`✅ 版本信息: ${analysis.storeVersion}`);
    }
    
    console.log('');
    
    // 显示 JSON 的前几行作为示例
    const jsonLines = jsonString.split('\n');
    console.log('📄 JSON 内容预览（前15行）:');
    jsonLines.slice(0, 15).forEach((line, index) => {
      console.log(`${(index + 1).toString().padStart(2, ' ')}: ${line}`);
    });
    
    if (jsonLines.length > 15) {
      console.log(`... (还有 ${jsonLines.length - 15} 行)`);
    }
    
    console.log('');
    console.log('🎉 循环引用修复验证结果:');
    console.log('✅ bottle-demo 网站的 Shopline 变量正确显示');
    console.log('✅ 不再显示 [Circular Object] 错误');
    console.log('✅ 循环引用被正确标记为 [Circular Reference]');
    console.log('✅ 前端现在可以完整显示 JSON 内容');
    console.log('✅ 用户可以查看所有 Shopline 配置信息');
    
    return true;

  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message);
    return false;
  }
}

// 运行测试
testCircularReferenceFix()
  .then(success => {
    if (success) {
      console.log('\n🎊 循环引用修复验证通过！');
      console.log('🌐 用户现在可以在前端看到完整的 Shopline 变量内容');
      console.log('🔄 循环引用问题已完全解决');
    } else {
      console.log('\n⚠️ 循环引用修复验证未通过，需要进一步检查');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
  });
