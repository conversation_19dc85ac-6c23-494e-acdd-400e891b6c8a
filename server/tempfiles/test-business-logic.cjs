/**
 * 测试新的业务逻辑
 */

const http = require('http');

async function testShoplineDetection(url, description) {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            url: url,
            options: {
                timeout: 30000,
                waitUntil: "domcontentloaded"
            }
        });

        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/detect/shopline',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    description,
                    status: res.statusCode,
                    data: JSON.parse(data)
                });
            });
        });

        req.on('error', reject);
        req.setTimeout(60000);
        req.write(postData);
        req.end();
    });
}

function analyzeResult(result) {
    console.log(`\n📊 ${result.description}:`);
    console.log('=' .repeat(50));
    
    if (result.data.success) {
        const { results, analysis } = result.data.data;
        
        // 显示检测到的变量
        console.log('🔍 检测到的变量:');
        results.forEach(variable => {
            const status = variable.found ? '✅' : '❌';
            const size = variable.size ? ` (${variable.size.toLocaleString()} 字符)` : '';
            console.log(`   ${status} ${variable.name} (${variable.type})${size}`);
            
            // 显示特殊变量的值
            if (variable.name === 'storeVersion' && variable.found) {
                console.log(`       🎯 版本值: ${variable.value}`);
            }
        });
        
        // 显示分析结果
        console.log('\n📈 分析结果:');
        console.log(`   🏪 是否为 SHOPLINE: ${analysis.isShoplineStore ? '是' : '否'}`);
        console.log(`   🏷️ 网站类型: ${analysis.storeType}`);
        console.log(`   📦 版本: ${analysis.storeVersion || '未知'}`);
        console.log(`   🎯 置信度: ${analysis.confidence}%`);
        console.log(`   📊 找到变量: ${analysis.totalVariablesFound} 个`);
        console.log(`   📏 数据大小: ${analysis.totalDataSize?.toLocaleString() || 0} 字符`);
        
        // 显示特性检测
        console.log('\n🔧 特性检测:');
        console.log(`   ⚙️ 环境配置: ${analysis.hasEnvConfig ? '是' : '否'}`);
        console.log(`   🔧 Shopline SDK: ${analysis.hasShoplineSDK ? '是' : '否'}`);
        console.log(`   📋 主配置: ${analysis.hasMainConfig ? '是' : '否'}`);
        
        // 验证业务逻辑
        const hasMainConfig = results.some(r => r.name === 'mainConfig' && r.found);
        const hasShopline = results.some(r => r.name === 'Shopline' && r.found);
        const hasStoreVersion = results.some(r => r.name === 'storeVersion' && r.found);
        const storeVersionValue = results.find(r => r.name === 'storeVersion')?.value;
        
        console.log('\n🧪 业务逻辑验证:');
        if (hasMainConfig && !hasShopline) {
            console.log('   ✅ 符合逻辑1: mainConfig 存在，Shopline 不存在');
            console.log(`   ✅ 应返回版本: 1.0，实际返回: ${storeVersionValue}`);
            console.log(`   ✅ 版本正确: ${storeVersionValue === 1.0 ? '是' : '否'}`);
        } else if (!hasMainConfig && hasShopline) {
            console.log('   ✅ 符合逻辑2: mainConfig 不存在，Shopline 存在');
            console.log(`   ✅ 应返回版本: 2.0，实际返回: ${storeVersionValue}`);
            console.log(`   ✅ 版本正确: ${storeVersionValue === 2.0 ? '是' : '否'}`);
        } else {
            console.log('   ℹ️ 不符合特定逻辑，使用默认处理');
            console.log(`   📊 mainConfig: ${hasMainConfig ? '存在' : '不存在'}`);
            console.log(`   📊 Shopline: ${hasShopline ? '存在' : '不存在'}`);
        }
        
        return {
            success: true,
            hasMainConfig,
            hasShopline,
            storeVersion: storeVersionValue,
            confidence: analysis.confidence
        };
        
    } else {
        console.log(`❌ 检测失败: ${result.data.error}`);
        return { success: false };
    }
}

async function runBusinessLogicTest() {
    console.log('🧪 测试新的业务逻辑...\n');
    console.log('🎯 测试目标:');
    console.log('   1. mainConfig 存在，Shopline 不存在 -> 返回 mainConfig + 版本 1.0');
    console.log('   2. mainConfig 不存在，Shopline 存在 -> 返回 Shopline + __ENV__ + 版本 2.0');

    try {
        // 测试现有的 SHOPLINE 网站
        const testCases = [
            {
                url: 'https://charm-demo.myshopline.com/',
                description: 'SHOPLINE 演示网站 (预期: Shopline 存在)'
            }
        ];
        
        const results = [];
        
        for (const testCase of testCases) {
            console.log(`\n🔍 测试: ${testCase.description}`);
            console.log(`📡 URL: ${testCase.url}`);
            
            const result = await testShoplineDetection(testCase.url, testCase.description);
            const analysis = analyzeResult(result);
            results.push(analysis);
        }
        
        // 总结测试结果
        console.log('\n📋 测试总结:');
        console.log('=' .repeat(50));
        
        const successfulTests = results.filter(r => r.success);
        console.log(`✅ 成功测试: ${successfulTests.length}/${results.length}`);
        
        successfulTests.forEach((result, index) => {
            console.log(`\n测试 ${index + 1}:`);
            console.log(`   mainConfig: ${result.hasMainConfig ? '存在' : '不存在'}`);
            console.log(`   Shopline: ${result.hasShopline ? '存在' : '不存在'}`);
            console.log(`   版本: ${result.storeVersion || '未设置'}`);
            console.log(`   置信度: ${result.confidence}%`);
        });
        
        console.log('\n🎉 业务逻辑测试完成！');
        console.log('💡 提示: 要测试逻辑1 (mainConfig存在，Shopline不存在)，需要找到符合条件的网站');
        console.log('💡 提示: 当前测试网站符合逻辑2 (mainConfig不存在，Shopline存在)');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

runBusinessLogicTest();
