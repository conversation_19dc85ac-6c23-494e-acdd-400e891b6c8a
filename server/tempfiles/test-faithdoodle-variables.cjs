/**
 * 测试 faithdoodle.shoplineapp.com 网站的变量
 * 检查该网站实际有哪些变量可用
 */

const { chromium } = require('playwright');

async function testFaithdoodleVariables() {
  console.log('🧪 测试 faithdoodle.shoplineapp.com 网站变量\n');

  const testUrl = 'https://faithdoodle.shoplineapp.com/';
  let browser = null;
  let page = null;

  try {
    console.log('🚀 启动浏览器...');
    browser = await chromium.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu'
      ]
    });

    page = await browser.newPage();
    
    console.log(`🌐 导航到: ${testUrl}`);
    await page.goto(testUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });

    // 等待页面稳定
    console.log('⏳ 等待页面稳定...');
    await page.waitForTimeout(5000);

    // 检测所有可能的变量
    console.log('🔍 检测变量...');
    
    const variablesToCheck = [
      'Shopline',
      '__ENV__', 
      'ShoplineAnalyticsAPI',
      'ShoplineCheckout',
      'mainConfig',
      'dataLayer',
      'gtag',
      'fbq',
      '__SHOPLINE_CONFIG__',
      'shoplineConfig',
      'SHOPLINE',
      'shopline',
      'config',
      'appConfig',
      'storeConfig',
      'themeConfig'
    ];

    const results = await page.evaluate((variables) => {
      const results = [];
      
      for (const varName of variables) {
        try {
          const exists = typeof window[varName] !== 'undefined';
          const value = exists ? window[varName] : undefined;
          const type = typeof value;
          
          let size = 0;
          let preview = null;
          
          if (exists) {
            try {
              if (type === 'object' && value !== null) {
                const keys = Object.keys(value);
                preview = `{${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}}`;
                size = JSON.stringify(value).length;
              } else if (type === 'string') {
                size = value.length;
                preview = value.length > 50 ? value.substring(0, 50) + '...' : value;
              } else {
                preview = String(value);
                size = preview.length;
              }
            } catch (e) {
              preview = '[Circular or Complex Object]';
              size = 0;
            }
          }
          
          results.push({
            name: varName,
            exists,
            type,
            size,
            preview
          });
        } catch (error) {
          results.push({
            name: varName,
            exists: false,
            type: 'error',
            size: 0,
            preview: error.message
          });
        }
      }
      
      return results;
    }, variablesToCheck);

    console.log('\n📊 变量检测结果:');
    console.log('='.repeat(80));
    
    const foundVariables = results.filter(r => r.exists);
    const missingVariables = results.filter(r => !r.exists);
    
    if (foundVariables.length > 0) {
      console.log('\n✅ 找到的变量:');
      foundVariables.forEach(result => {
        console.log(`  🔹 ${result.name} (${result.type})`);
        console.log(`     大小: ${result.size.toLocaleString()} 字符`);
        console.log(`     预览: ${result.preview}`);
        console.log('');
      });
    } else {
      console.log('\n❌ 未找到任何目标变量');
    }
    
    console.log('\n❌ 缺失的变量:');
    missingVariables.forEach(result => {
      console.log(`  ⚪ ${result.name}`);
    });

    // 检查页面的全局对象
    console.log('\n🔍 检查页面全局对象...');
    const globalInfo = await page.evaluate(() => {
      const globals = [];
      
      // 检查 window 对象的所有属性
      for (const key in window) {
        if (key.toLowerCase().includes('shop') || 
            key.toLowerCase().includes('config') ||
            key.toLowerCase().includes('env') ||
            key.toLowerCase().includes('main')) {
          try {
            const value = window[key];
            const type = typeof value;
            globals.push({
              name: key,
              type,
              isShoplineRelated: key.toLowerCase().includes('shop')
            });
          } catch (e) {
            // 忽略无法访问的属性
          }
        }
      }
      
      return {
        globals,
        userAgent: navigator.userAgent,
        url: location.href,
        title: document.title
      };
    });

    console.log(`\n📄 页面信息:`);
    console.log(`  标题: ${globalInfo.title}`);
    console.log(`  URL: ${globalInfo.url}`);
    
    if (globalInfo.globals.length > 0) {
      console.log('\n🌐 相关全局变量:');
      globalInfo.globals.forEach(global => {
        const marker = global.isShoplineRelated ? '🎯' : '📦';
        console.log(`  ${marker} ${global.name} (${global.type})`);
      });
    } else {
      console.log('\n⚠️ 未找到相关的全局变量');
    }

    // 分析结果
    console.log('\n🎯 分析结果:');
    console.log('='.repeat(80));
    
    const shoplineRelated = foundVariables.filter(v => 
      v.name.toLowerCase().includes('shopline') || 
      v.name.toLowerCase().includes('shop')
    );
    
    if (shoplineRelated.length > 0) {
      console.log('✅ 这是一个 SHOPLINE 网站');
      console.log(`   找到 ${shoplineRelated.length} 个 SHOPLINE 相关变量`);
    } else if (foundVariables.some(v => v.name === 'mainConfig')) {
      console.log('✅ 找到 mainConfig 变量，可能是 SHOPLINE 网站');
    } else {
      console.log('❓ 无法确定是否为 SHOPLINE 网站');
      console.log('   建议检查页面源码或网络请求');
    }

    // 建议
    console.log('\n💡 建议:');
    if (foundVariables.length === 0) {
      console.log('1. 增加页面等待时间，变量可能需要更长时间加载');
      console.log('2. 检查页面是否需要用户交互才能加载变量');
      console.log('3. 检查网络请求，变量可能通过 AJAX 异步加载');
    } else {
      console.log('1. 将找到的变量添加到检测配置中');
      console.log('2. 优化检测时机，确保变量完全加载');
    }

    return foundVariables.length > 0;

  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message);
    return false;
  } finally {
    if (page) {
      await page.close();
    }
    if (browser) {
      await browser.close();
    }
  }
}

// 运行测试
testFaithdoodleVariables()
  .then(success => {
    console.log(`\n${success ? '🎉' : '⚠️'} 测试完成`);
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
  });
