/**
 * 简化的服务器启动脚本
 */

const { serve } = require('@hono/node-server');

async function startServer() {
  try {
    // 动态导入编译后的服务器
    const serverModule = await import('./dist/server.js');
    const app = serverModule.default;

    console.log('🚀 Starting ShopLine Checker Server...');

    const server = serve({
      fetch: app.fetch,
      port: app.port || 3000
    }, (info) => {
      console.log(`✅ Server running on http://localhost:${info.port}`);
      console.log('📋 Available endpoints:');
      console.log('  GET  /health - Health check');
      console.log('  POST /detect - General detection');
      console.log('  POST /detect/shopline - Shopline detection');
      console.log('  GET  /stats - Statistics');
      console.log('  POST /cache/clear - Clear cache');
      console.log('  POST /browser/health - Browser health check');
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
