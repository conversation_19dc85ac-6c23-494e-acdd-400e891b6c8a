/**
 * 测试前端修复效果
 * 验证 API 返回的数据格式是否符合前端期望
 */

const fetch = require('node-fetch');

async function testFrontendFix() {
  console.log('🧪 测试前端修复效果\n');

  const baseUrl = 'http://localhost:3000';
  
  try {
    console.log('📋 测试 Shopline 检测 API 响应格式');
    
    const response = await fetch(`${baseUrl}/detect/shopline`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: 'https://charm-demo.myshopline.com/'
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      console.log('❌ API 请求失败:', data.error);
      return false;
    }

    const { results, analysis } = data.data;
    
    console.log('✅ API 请求成功');
    console.log('');
    
    // 检查前端期望的字段
    console.log('📊 检查前端期望的字段:');
    
    const expectedFields = [
      'isShoplineStore',
      'storeType', 
      'confidence',
      'totalVariablesFound',
      'totalDataSize'
    ];
    
    let allFieldsPresent = true;
    
    expectedFields.forEach(field => {
      const present = field in analysis;
      console.log(`${present ? '✅' : '❌'} ${field}: ${present ? analysis[field] : '缺失'}`);
      if (!present) allFieldsPresent = false;
    });
    
    // 检查可选字段
    if (analysis.storeVersion) {
      console.log(`✅ storeVersion (可选): ${analysis.storeVersion}`);
    }
    
    console.log('');
    
    // 模拟前端判断逻辑
    console.log('🎯 模拟前端判断逻辑:');
    
    if (analysis.isShoplineStore) {
      console.log('✅ 前端应该显示: "🎉 检测到 SHOPLINE 网站！"');
      console.log(`📊 置信度: ${analysis.confidence}%`);
      console.log(`🏪 商店类型: ${analysis.storeType}`);
      console.log(`📦 找到变量: ${analysis.totalVariablesFound} 个`);
      console.log(`💾 数据大小: ${analysis.totalDataSize.toLocaleString()} 字符`);
      
      if (analysis.storeVersion) {
        console.log(`🔖 版本信息: ${analysis.storeVersion}`);
      }
      
      console.log('');
      console.log('🎉 前端修复成功！');
      console.log('✅ 前端现在应该能正确显示 SHOPLINE 检测结果');
      
    } else {
      console.log('❌ 前端仍会显示: "未检测到 SHOPLINE 变量"');
      console.log('❌ 修复失败，需要进一步检查');
      allFieldsPresent = false;
    }
    
    console.log('');
    
    // 检查检测到的变量
    console.log('📦 检测到的变量:');
    const foundVariables = results.filter(r => r.found);
    foundVariables.forEach(variable => {
      console.log(`✅ ${variable.name} (${variable.type}) - ${variable.size} 字符`);
    });
    
    console.log('');
    
    if (allFieldsPresent && analysis.isShoplineStore) {
      console.log('🎊 测试通过！前端修复成功');
      console.log('📱 用户现在应该能看到正确的检测结果');
      return true;
    } else {
      console.log('⚠️ 测试未完全通过，仍有问题需要解决');
      return false;
    }

  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message);
    return false;
  }
}

// 运行测试
testFrontendFix()
  .then(success => {
    if (success) {
      console.log('\n🎉 前端修复验证通过！');
      console.log('🌐 用户现在应该能在前端看到正确的 SHOPLINE 检测结果');
    } else {
      console.log('\n⚠️ 前端修复验证未通过，需要进一步检查');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
  });
