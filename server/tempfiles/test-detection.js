/**
 * 测试 ShopLine Checker Server 的变量检测功能
 */

const https = require('https');
const http = require('http');

const testData = {
  url: "https://charm-demo.myshopline.com/",
  variables: [
    {
      name: "__ENV__",
      path: "__ENV__",
      defaultValue: null
    }
  ],
  options: {
    timeout: 30000,
    waitUntil: "domcontentloaded"
  }
};

const postData = JSON.stringify(testData);

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/detect',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('🔍 Testing ShopLine Checker Server...');
console.log(`📡 URL: ${testData.url}`);
console.log(`🎯 Variable: window.${testData.variables[0].path}`);
console.log('⏳ Sending request...\n');

const req = http.request(options, (res) => {
  console.log(`📊 Status Code: ${res.statusCode}`);
  console.log(`📋 Headers:`, res.headers);
  console.log('📄 Response:\n');

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('✅ Parsed Response:');
      console.log(JSON.stringify(response, null, 2));
      
      if (response.success && response.data && response.data.results) {
        const envResult = response.data.results.find(r => r.name === '__ENV__');
        if (envResult && envResult.found) {
          console.log('\n🎉 __ENV__ variable found!');
          console.log('📦 Value:');
          console.log(JSON.stringify(envResult.value, null, 2));
        } else {
          console.log('\n❌ __ENV__ variable not found');
        }
      }
    } catch (error) {
      console.error('❌ Error parsing response:', error);
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request error:', error);
});

req.on('timeout', () => {
  console.error('⏰ Request timeout');
  req.destroy();
});

req.setTimeout(60000); // 60 seconds timeout

req.write(postData);
req.end();
