/**
 * 测试 SHOPLINE 平台版本检测
 * 验证能正确识别 SHOPLINE 1.0 和 2.0 平台
 */

const fetch = require('node-fetch');

async function testShoplinePlatformDetection() {
  console.log('🧪 测试 SHOPLINE 平台版本检测\n');

  const baseUrl = 'http://localhost:3000';
  
  const testCases = [
    {
      name: 'SHOPLINE 1.0 平台',
      url: 'https://faithdoodle.shoplineapp.com/',
      expectedPlatform: '1.0',
      expectedVariables: ['mainConfig', 'shopline'],
      missingVariables: ['Shopline', '__ENV__']
    },
    {
      name: 'SHOPLINE 2.0 平台',
      url: 'https://bottle-demo.myshopline.com/',
      expectedPlatform: '2.0',
      expectedVariables: ['Shopline', '__ENV__'],
      missingVariables: ['mainConfig', 'shopline']
    }
  ];

  let allTestsPassed = true;

  for (const testCase of testCases) {
    console.log(`🔍 测试: ${testCase.name}`);
    console.log(`   URL: ${testCase.url}`);
    
    try {
      const response = await fetch(`${baseUrl}/detect/shopline`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: testCase.url
        })
      });
      
      const data = await response.json();
      
      if (!data.success) {
        console.log(`   ❌ API 请求失败: ${data.error}`);
        allTestsPassed = false;
        continue;
      }

      const { results, analysis } = data.data;
      
      console.log(`   ✅ API 请求成功`);
      
      // 检查是否识别为 SHOPLINE 网站
      if (!analysis.isShoplineStore) {
        console.log(`   ❌ 未识别为 SHOPLINE 网站`);
        allTestsPassed = false;
        continue;
      }
      
      console.log(`   ✅ 正确识别为 SHOPLINE 网站`);
      
      // 检查平台版本
      const expectedStoreType = `SHOPLINE ${testCase.expectedPlatform} Store`;
      if (analysis.storeType !== expectedStoreType) {
        console.log(`   ❌ 平台类型错误: 期望 "${expectedStoreType}", 实际 "${analysis.storeType}"`);
        allTestsPassed = false;
      } else {
        console.log(`   ✅ 平台类型正确: ${analysis.storeType}`);
      }
      
      // 检查版本信息
      if (analysis.storeVersion && analysis.storeVersion.includes(`Platform: ${testCase.expectedPlatform}`)) {
        console.log(`   ✅ 版本信息正确: ${analysis.storeVersion}`);
      } else {
        console.log(`   ❌ 版本信息错误: ${analysis.storeVersion}`);
        allTestsPassed = false;
      }
      
      // 检查找到的变量
      const foundVariables = analysis.foundVariables;
      let variablesCorrect = true;
      
      for (const expectedVar of testCase.expectedVariables) {
        if (!foundVariables.includes(expectedVar)) {
          console.log(`   ❌ 缺少期望变量: ${expectedVar}`);
          variablesCorrect = false;
          allTestsPassed = false;
        }
      }
      
      for (const missingVar of testCase.missingVariables) {
        if (foundVariables.includes(missingVar)) {
          console.log(`   ❌ 不应该找到变量: ${missingVar}`);
          variablesCorrect = false;
          allTestsPassed = false;
        }
      }
      
      if (variablesCorrect) {
        console.log(`   ✅ 变量检测正确: [${foundVariables.join(', ')}]`);
      }
      
      // 检查置信度
      if (analysis.confidence >= 75) {
        console.log(`   ✅ 置信度良好: ${analysis.confidence}%`);
      } else {
        console.log(`   ⚠️ 置信度偏低: ${analysis.confidence}%`);
      }
      
      // 检查数据大小
      if (analysis.totalDataSize > 0) {
        console.log(`   ✅ 数据大小: ${analysis.totalDataSize.toLocaleString()} 字符`);
      } else {
        console.log(`   ⚠️ 数据大小为 0`);
      }
      
      console.log('');

    } catch (error) {
      console.log(`   💥 测试过程中发生错误: ${error.message}`);
      allTestsPassed = false;
    }
  }

  // 总结
  console.log('🎯 测试总结:');
  console.log('='.repeat(80));
  
  if (allTestsPassed) {
    console.log('✅ 所有测试通过！');
    console.log('🎉 SHOPLINE 平台版本检测功能正常工作');
    console.log('');
    console.log('📊 功能验证:');
    console.log('  ✅ 正确识别 SHOPLINE 1.0 平台 (mainConfig + shopline)');
    console.log('  ✅ 正确识别 SHOPLINE 2.0 平台 (Shopline + __ENV__)');
    console.log('  ✅ 准确的平台版本信息');
    console.log('  ✅ 合理的置信度计算');
    console.log('  ✅ 完整的变量检测');
  } else {
    console.log('❌ 部分测试失败');
    console.log('⚠️ 需要进一步检查和修复');
  }

  return allTestsPassed;
}

// 运行测试
testShoplinePlatformDetection()
  .then(success => {
    console.log(`\n${success ? '🎊' : '⚠️'} 平台检测测试完成`);
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
  });
