/**
 * 全面浏览器诊断脚本 v2.0
 * 深度分析 bottle-demo.myshopline.com 的浏览器断开连接问题
 */

const puppeteer = require('puppeteer');

// 测试配置
const TEST_CONFIGS = [
  {
    name: '基础配置',
    config: {
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
    }
  },
  {
    name: '新 Headless 模式',
    config: {
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
    }
  },
  {
    name: '稳定配置',
    config: {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    }
  },
  {
    name: '最小配置',
    config: {
      headless: true,
      args: ['--no-sandbox']
    }
  }
];

// 等待策略
const WAIT_STRATEGIES = [
  { name: 'domcontentloaded', waitUntil: 'domcontentloaded' },
  { name: 'load', waitUntil: 'load' },
  { name: 'networkidle0', waitUntil: 'networkidle0' },
  { name: 'networkidle2', waitUntil: 'networkidle2' }
];

// 变量检测时机
const DETECTION_TIMINGS = [
  { name: '立即检测', delay: 0 },
  { name: '1秒后', delay: 1000 },
  { name: '3秒后', delay: 3000 },
  { name: '5秒后', delay: 5000 }
];

async function logStep(step, details = '') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${step}${details ? ': ' + details : ''}`);
}

async function testBrowserConfig(config, testUrl) {
  await logStep(`🧪 测试配置: ${config.name}`);
  
  let browser = null;
  let page = null;
  let success = false;
  let error = null;
  
  try {
    await logStep('🚀 启动浏览器', JSON.stringify(config.config));
    
    browser = await puppeteer.launch({
      ...config.config,
      timeout: 30000,
      protocolTimeout: 30000
    });
    
    await logStep('✅ 浏览器启动成功');
    
    // 监听浏览器事件
    browser.on('disconnected', () => {
      logStep('❌ 浏览器断开连接事件触发');
    });
    
    await logStep('📄 创建页面');
    page = await browser.newPage();
    
    // 监听页面事件
    let frameDetachedCount = 0;
    let pageErrorCount = 0;
    
    page.on('error', (err) => {
      pageErrorCount++;
      logStep('❌ 页面错误', err.message);
    });
    
    page.on('framedetached', (frame) => {
      frameDetachedCount++;
      logStep('🔗 框架分离', frame.url());
    });
    
    page.on('framenavigated', (frame) => {
      logStep('🧭 框架导航', frame.url());
    });
    
    // 测试不同的等待策略
    for (const strategy of WAIT_STRATEGIES) {
      try {
        await logStep(`🌐 尝试导航 (${strategy.name})`);
        
        const startTime = Date.now();
        await page.goto(testUrl, {
          waitUntil: strategy.waitUntil,
          timeout: 15000
        });
        const loadTime = Date.now() - startTime;
        
        await logStep(`✅ 导航成功 (${strategy.name})`, `耗时: ${loadTime}ms`);
        
        // 检查浏览器状态
        const browserConnected = browser.isConnected();
        const pageClosed = page.isClosed();
        
        await logStep('🔍 状态检查', `浏览器连接: ${browserConnected}, 页面关闭: ${pageClosed}`);
        
        if (!browserConnected || pageClosed) {
          throw new Error('浏览器或页面状态异常');
        }
        
        // 测试不同的检测时机
        for (const timing of DETECTION_TIMINGS) {
          try {
            await logStep(`⏳ 等待 ${timing.name}`);
            
            if (timing.delay > 0) {
              await new Promise(resolve => setTimeout(resolve, timing.delay));
            }
            
            // 检测 Shopline 变量
            await logStep('🔍 检测 window.Shopline');
            
            const shoplineExists = await page.evaluate(() => {
              return typeof window.Shopline !== 'undefined';
            });
            
            await logStep('📊 检测结果', `window.Shopline 存在: ${shoplineExists}`);
            
            if (shoplineExists) {
              const shoplineType = await page.evaluate(() => {
                return typeof window.Shopline;
              });
              
              await logStep('✅ 成功检测到 Shopline', `类型: ${shoplineType}`);
              success = true;
              break;
            }
            
          } catch (detectionError) {
            await logStep('❌ 检测失败', detectionError.message);
          }
        }
        
        if (success) break;
        
      } catch (navError) {
        await logStep('❌ 导航失败', `${strategy.name}: ${navError.message}`);
        
        // 检查是否是致命错误
        if (navError.message.includes('frame was detached') ||
            navError.message.includes('Protocol error') ||
            navError.message.includes('Browser disconnected')) {
          error = navError;
          break;
        }
      }
    }
    
    await logStep('📈 统计信息', `框架分离: ${frameDetachedCount}, 页面错误: ${pageErrorCount}`);
    
  } catch (configError) {
    error = configError;
    await logStep('💥 配置测试失败', configError.message);
  } finally {
    // 清理资源
    try {
      if (page && !page.isClosed()) {
        await page.close();
        await logStep('🧹 页面已关闭');
      }
    } catch (e) {
      await logStep('⚠️ 页面关闭失败', e.message);
    }
    
    try {
      if (browser && browser.isConnected()) {
        await browser.close();
        await logStep('🧹 浏览器已关闭');
      }
    } catch (e) {
      await logStep('⚠️ 浏览器关闭失败', e.message);
    }
  }
  
  return { success, error, config: config.name };
}

async function comprehensiveDiagnosis() {
  console.log('🔬 开始全面浏览器诊断...\n');
  
  const testUrl = 'https://bottle-demo.myshopline.com/';
  const results = [];
  
  await logStep('🎯 目标URL', testUrl);
  
  // 测试所有配置
  for (const config of TEST_CONFIGS) {
    console.log('\n' + '='.repeat(60));
    const result = await testBrowserConfig(config, testUrl);
    results.push(result);
    
    // 在配置之间稍作等待
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 分析结果
  console.log('\n' + '='.repeat(60));
  await logStep('📊 诊断结果分析');
  
  const successfulConfigs = results.filter(r => r.success);
  const failedConfigs = results.filter(r => !r.success);
  
  console.log(`\n✅ 成功配置 (${successfulConfigs.length}/${results.length}):`);
  successfulConfigs.forEach(r => console.log(`  - ${r.config}`));
  
  console.log(`\n❌ 失败配置 (${failedConfigs.length}/${results.length}):`);
  failedConfigs.forEach(r => console.log(`  - ${r.config}: ${r.error?.message || '未知错误'}`));
  
  // 根本原因分析
  console.log('\n🔍 根本原因分析:');
  
  if (successfulConfigs.length === 0) {
    console.log('❌ 所有配置都失败了，可能的原因:');
    console.log('  1. 网站有强烈的反自动化检测');
    console.log('  2. 网络连接问题');
    console.log('  3. Puppeteer 版本兼容性问题');
    console.log('  4. 系统环境问题');
  } else if (successfulConfigs.length < results.length) {
    console.log('⚠️ 部分配置成功，建议:');
    console.log(`  - 使用成功的配置: ${successfulConfigs[0].config}`);
    console.log('  - 避免使用失败的配置');
  } else {
    console.log('✅ 所有配置都成功，问题可能在于:');
    console.log('  1. 服务器环境与测试环境不同');
    console.log('  2. 并发处理导致的资源竞争');
    console.log('  3. 长时间运行导致的内存泄漏');
  }
  
  console.log('\n🎉 诊断完成');
}

// 运行诊断
comprehensiveDiagnosis().catch(console.error);
