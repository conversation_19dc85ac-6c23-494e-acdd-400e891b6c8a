/**
 * 静态分析集成测试套件
 * 测试静态分析模块与主服务器的集成
 */

const { spawn } = require('child_process');
const fetch = require('node-fetch');

// 测试配置
const TEST_CONFIG = {
  serverUrl: 'http://localhost:3001',
  testUrls: [
    'https://demo-store.shoplineapp.com',
    'https://www.shopify.com', // 非SHOPLINE网站
    'https://invalid-url-for-testing.com' // 无效URL
  ],
  timeout: 30000
};

class IntegrationTestSuite {
  constructor() {
    this.serverProcess = null;
    this.testResults = [];
  }

  /**
   * 启动测试服务器
   */
  async startServer() {
    console.log('🚀 Starting test server...');
    
    return new Promise((resolve, reject) => {
      this.serverProcess = spawn('npm', ['run', 'dev'], {
        cwd: process.cwd(),
        stdio: 'pipe'
      });

      let serverReady = false;
      const timeout = setTimeout(() => {
        if (!serverReady) {
          reject(new Error('Server startup timeout'));
        }
      }, 15000);

      this.serverProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log('Server:', output.trim());
        
        if (output.includes('Server running on')) {
          serverReady = true;
          clearTimeout(timeout);
          setTimeout(resolve, 2000); // 等待服务器完全启动
        }
      });

      this.serverProcess.stderr.on('data', (data) => {
        console.error('Server Error:', data.toString());
      });

      this.serverProcess.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * 停止测试服务器
   */
  async stopServer() {
    if (this.serverProcess) {
      console.log('🛑 Stopping test server...');
      this.serverProcess.kill('SIGTERM');
      
      return new Promise((resolve) => {
        this.serverProcess.on('exit', () => {
          console.log('✅ Server stopped');
          resolve();
        });
        
        // 强制杀死进程
        setTimeout(() => {
          if (this.serverProcess) {
            this.serverProcess.kill('SIGKILL');
            resolve();
          }
        }, 5000);
      });
    }
  }

  /**
   * 测试健康检查端点
   */
  async testHealthCheck() {
    console.log('\n📋 Testing health check endpoint...');
    
    try {
      const response = await fetch(`${TEST_CONFIG.serverUrl}/health`);
      const data = await response.json();
      
      const success = response.ok && 
                     data.success && 
                     data.data.status === 'healthy' &&
                     data.data.staticAnalysis &&
                     typeof data.data.staticAnalysis.healthy === 'boolean';
      
      this.addTestResult('Health Check', success, {
        status: response.status,
        data: data.data
      });
      
      return success;
    } catch (error) {
      this.addTestResult('Health Check', false, { error: error.message });
      return false;
    }
  }

  /**
   * 测试统计端点
   */
  async testStatsEndpoint() {
    console.log('\n📊 Testing stats endpoint...');
    
    try {
      const response = await fetch(`${TEST_CONFIG.serverUrl}/stats`);
      const data = await response.json();
      
      const success = response.ok && 
                     data.success && 
                     data.data.browser &&
                     data.data.cache &&
                     data.data.staticAnalysis &&
                     data.data.modeSelection;
      
      this.addTestResult('Stats Endpoint', success, {
        status: response.status,
        hasStaticAnalysis: !!data.data?.staticAnalysis,
        hasModeSelection: !!data.data?.modeSelection
      });
      
      return success;
    } catch (error) {
      this.addTestResult('Stats Endpoint', false, { error: error.message });
      return false;
    }
  }

  /**
   * 测试静态分析端点
   */
  async testStaticAnalysisEndpoint() {
    console.log('\n🔬 Testing static analysis endpoint...');
    
    const testUrl = TEST_CONFIG.testUrls[0];
    
    try {
      const response = await fetch(`${TEST_CONFIG.serverUrl}/analyze/static`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: testUrl })
      });
      
      const data = await response.json();
      
      const success = response.ok && 
                     data.success && 
                     data.data.method === 'static-analysis' &&
                     data.data.url === testUrl &&
                     Array.isArray(data.data.results) &&
                     data.data.analysis &&
                     typeof data.data.timing === 'object';
      
      this.addTestResult('Static Analysis Endpoint', success, {
        status: response.status,
        method: data.data?.method,
        foundVariables: data.data?.analysis?.foundCount,
        timing: data.data?.timing?.total
      });
      
      return success;
    } catch (error) {
      this.addTestResult('Static Analysis Endpoint', false, { error: error.message });
      return false;
    }
  }

  /**
   * 测试增强检测端点
   */
  async testEnhancedDetectionEndpoint() {
    console.log('\n🚀 Testing enhanced detection endpoint...');
    
    const testUrl = TEST_CONFIG.testUrls[0];
    
    try {
      // 测试自动模式
      const response = await fetch(`${TEST_CONFIG.serverUrl}/detect/enhanced`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          url: testUrl,
          options: { mode: 'auto' }
        })
      });
      
      const data = await response.json();
      
      const success = response.ok && 
                     data.success && 
                     ['static-analysis', 'dynamic', 'hybrid'].includes(data.data.method) &&
                     data.data.url === testUrl &&
                     Array.isArray(data.data.results) &&
                     data.data.analysis;
      
      this.addTestResult('Enhanced Detection Endpoint', success, {
        status: response.status,
        method: data.data?.method,
        foundVariables: data.data?.analysis?.foundCount,
        timing: data.data?.timing?.total,
        isHybrid: data.data?.method === 'hybrid'
      });
      
      return success;
    } catch (error) {
      this.addTestResult('Enhanced Detection Endpoint', false, { error: error.message });
      return false;
    }
  }

  /**
   * 测试批量静态分析端点
   */
  async testBatchStaticAnalysis() {
    console.log('\n📦 Testing batch static analysis endpoint...');
    
    const testUrls = TEST_CONFIG.testUrls.slice(0, 2); // 只测试前两个URL
    
    try {
      const response = await fetch(`${TEST_CONFIG.serverUrl}/analyze/static/batch`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ urls: testUrls })
      });
      
      const data = await response.json();
      
      const success = response.ok && 
                     data.success && 
                     Array.isArray(data.data.results) &&
                     data.data.results.length === testUrls.length &&
                     data.data.summary &&
                     typeof data.data.summary.total === 'number';
      
      this.addTestResult('Batch Static Analysis', success, {
        status: response.status,
        resultCount: data.data?.results?.length,
        summary: data.data?.summary
      });
      
      return success;
    } catch (error) {
      this.addTestResult('Batch Static Analysis', false, { error: error.message });
      return false;
    }
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    console.log('\n❌ Testing error handling...');
    
    try {
      // 测试无效URL
      const response = await fetch(`${TEST_CONFIG.serverUrl}/analyze/static`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: 'invalid-url' })
      });
      
      const data = await response.json();
      
      const success = response.status === 400 && !data.success;
      
      this.addTestResult('Error Handling', success, {
        status: response.status,
        errorHandled: !data.success
      });
      
      return success;
    } catch (error) {
      this.addTestResult('Error Handling', false, { error: error.message });
      return false;
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, details = {}) {
    this.testResults.push({
      name: testName,
      success,
      details,
      timestamp: new Date().toISOString()
    });
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${success ? 'PASSED' : 'FAILED'}`);
    
    if (!success || process.env.VERBOSE) {
      console.log('   Details:', JSON.stringify(details, null, 2));
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 Starting Static Analysis Integration Tests\n');
    
    try {
      // 启动服务器
      await this.startServer();
      
      // 运行测试
      await this.testHealthCheck();
      await this.testStatsEndpoint();
      await this.testStaticAnalysisEndpoint();
      await this.testEnhancedDetectionEndpoint();
      await this.testBatchStaticAnalysis();
      await this.testErrorHandling();
      
      // 生成报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    } finally {
      // 停止服务器
      await this.stopServer();
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📊 Test Results Summary');
    console.log('========================');
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.name}: ${JSON.stringify(r.details)}`);
        });
      
      process.exit(1);
    } else {
      console.log('\n🎉 All tests passed!');
      process.exit(0);
    }
  }
}

// 运行测试
if (require.main === module) {
  const testSuite = new IntegrationTestSuite();
  testSuite.runAllTests().catch(error => {
    console.error('Test suite error:', error);
    process.exit(1);
  });
}

module.exports = IntegrationTestSuite;
