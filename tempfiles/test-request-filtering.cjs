/**
 * 测试请求过滤功能
 * 验证第三方分析服务的警告是否被正确过滤
 */

const fetch = require('node-fetch');

const SERVER_URL = 'http://localhost:3001';

// 测试网站 - 包含大量第三方分析服务的SHOPLINE网站
const TEST_SITES = [
  {
    name: 'BLACHOICE Store',
    url: 'https://www.blachoice.com/',
    description: '包含Google Analytics和Shoplytics的SHOPLINE网站'
  },
  {
    name: 'FaithDoodle Store',
    url: 'https://faithdoodle.shoplineapp.com/',
    description: '现代SHOPLINE 2.0网站'
  }
];

class RequestFilteringTest {
  constructor() {
    this.results = [];
  }

  /**
   * 等待服务器就绪
   */
  async waitForServer(maxAttempts = 5) {
    console.log('🔍 Checking if server is running...');
    
    for (let i = 0; i < maxAttempts; i++) {
      try {
        const response = await fetch(`${SERVER_URL}/health`, { timeout: 5000 });
        if (response.ok) {
          console.log('✅ Server is ready');
          return true;
        }
      } catch (error) {
        console.log(`⏳ Attempt ${i + 1}/${maxAttempts}: Server not ready, waiting...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    throw new Error('Server is not running. Please start the server first with: npm run dev');
  }

  /**
   * 测试动态检测 - 观察日志中的警告
   */
  async testDynamicDetectionWithFiltering(site) {
    console.log(`\n🎭 Testing dynamic detection for: ${site.name}`);
    console.log(`📍 URL: ${site.url}`);
    console.log(`📝 Description: ${site.description}`);
    
    try {
      const startTime = Date.now();
      
      console.log('   🚀 Starting dynamic detection...');
      console.log('   📋 Monitor server logs for request filtering behavior');
      
      const response = await fetch(`${SERVER_URL}/detect/enhanced`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          url: site.url,
          options: { 
            mode: 'dynamic',
            timeout: 45000
          }
        })
      });
      
      const responseTime = Date.now() - startTime;
      const data = await response.json();
      
      const result = {
        site: site.name,
        url: site.url,
        success: response.ok && data.success,
        responseTime,
        method: data.data?.method,
        foundVariables: data.data?.analysis?.foundCount || 0,
        variables: data.data?.results?.filter(v => v.found) || [],
        hasShopline: data.data?.analysis?.hasShopline,
        confidence: data.data?.analysis?.confidence || 0
      };
      
      this.results.push(result);
      
      console.log(`   ✅ Detection completed:`);
      console.log(`     - Success: ${result.success}`);
      console.log(`     - Response time: ${responseTime}ms`);
      console.log(`     - Method: ${result.method}`);
      console.log(`     - Found variables: ${result.foundVariables}`);
      console.log(`     - Has SHOPLINE: ${result.hasShopline}`);
      console.log(`     - Confidence: ${(result.confidence * 100).toFixed(1)}%`);
      
      if (result.variables.length > 0) {
        console.log(`     - Variables found:`);
        result.variables.forEach(v => {
          console.log(`       * ${v.name}: ${v.size || 0} chars`);
        });
      }
      
      console.log(`   📋 Check server logs above for request filtering behavior`);
      console.log(`   🔍 Look for filtered analytics requests (should be DEBUG level)`);
      
      return result;
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      return {
        site: site.name,
        url: site.url,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 测试静态分析 - 对比性能
   */
  async testStaticAnalysisComparison(site) {
    console.log(`\n🔬 Testing static analysis for: ${site.name}`);
    
    try {
      const startTime = Date.now();
      
      const response = await fetch(`${SERVER_URL}/analyze/static`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          url: site.url,
          options: {
            enablePerformanceMetrics: true
          }
        })
      });
      
      const responseTime = Date.now() - startTime;
      const data = await response.json();
      
      console.log(`   ✅ Static analysis completed:`);
      console.log(`     - Success: ${response.ok && data.success}`);
      console.log(`     - Response time: ${responseTime}ms`);
      console.log(`     - Found variables: ${data.data?.analysis?.foundCount || 0}`);
      console.log(`     - Confidence: ${((data.data?.analysis?.confidence || 0) * 100).toFixed(1)}%`);
      console.log(`   📋 Static analysis should have minimal request filtering (fewer third-party requests)`);
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }

  /**
   * 测试混合模式 - 观察两种方法的请求过滤差异
   */
  async testHybridModeFiltering(site) {
    console.log(`\n🔀 Testing hybrid mode for: ${site.name}`);
    
    try {
      const startTime = Date.now();
      
      console.log('   🚀 Starting hybrid detection...');
      console.log('   📋 This will run both static and dynamic detection');
      console.log('   🔍 Observe request filtering differences between methods');
      
      const response = await fetch(`${SERVER_URL}/detect/enhanced`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          url: site.url,
          options: { 
            mode: 'hybrid',
            enableComparison: true,
            timeout: 60000
          }
        })
      });
      
      const responseTime = Date.now() - startTime;
      const data = await response.json();
      
      console.log(`   ✅ Hybrid detection completed:`);
      console.log(`     - Success: ${response.ok && data.success}`);
      console.log(`     - Response time: ${responseTime}ms`);
      console.log(`     - Method: ${data.data?.method}`);
      console.log(`     - Found variables: ${data.data?.analysis?.foundCount || 0}`);
      
      if (data.data?.comparison) {
        console.log(`     - Comparison results:`);
        console.log(`       * Variable agreement: ${(data.data.comparison.agreement?.variableAgreement * 100).toFixed(1)}%`);
        console.log(`       * Overall agreement: ${(data.data.comparison.agreement?.overallAgreement * 100).toFixed(1)}%`);
        console.log(`       * Recommended result: ${data.data.comparison.recommendedResult}`);
      }
      
      console.log(`   📋 Check logs for request filtering in both static and dynamic phases`);
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log(`\n📊 === 请求过滤测试报告 ===`);
    
    console.log(`\n🎯 测试目标:`);
    console.log(`   - 验证第三方分析服务请求失败的警告被正确过滤`);
    console.log(`   - 确保核心SHOPLINE变量检测功能不受影响`);
    console.log(`   - 观察不同检测模式的请求过滤行为`);
    
    console.log(`\n📈 测试结果:`);
    const successfulTests = this.results.filter(r => r.success).length;
    const totalTests = this.results.length;
    
    console.log(`   总测试数: ${totalTests}`);
    console.log(`   成功测试: ${successfulTests}`);
    console.log(`   成功率: ${totalTests > 0 ? ((successfulTests / totalTests) * 100).toFixed(1) : 0}%`);
    
    if (this.results.length > 0) {
      console.log(`\n🔍 详细结果:`);
      this.results.forEach(result => {
        console.log(`   🏪 ${result.site}:`);
        console.log(`     - 检测成功: ${result.success ? '✅' : '❌'}`);
        if (result.success) {
          console.log(`     - 找到变量: ${result.foundVariables}`);
          console.log(`     - SHOPLINE检测: ${result.hasShopline ? '✅' : '❌'}`);
          console.log(`     - 置信度: ${(result.confidence * 100).toFixed(1)}%`);
          console.log(`     - 响应时间: ${result.responseTime}ms`);
        } else {
          console.log(`     - 错误: ${result.error || 'Unknown error'}`);
        }
      });
    }
    
    console.log(`\n📋 日志分析指南:`);
    console.log(`   ✅ 期望看到的日志:`);
    console.log(`     - [DEBUG] Third-party analytics request failed (expected)`);
    console.log(`     - 过滤的域名: analytics.google.com, events.shoplytics.com`);
    console.log(`     - 正常的变量检测日志`);
    
    console.log(`\n   ❌ 不应该看到的日志:`);
    console.log(`     - [WARN] Request failed (对于已知的分析服务)`);
    console.log(`     - 大量的第三方服务警告`);
    
    console.log(`\n🎯 结论:`);
    if (successfulTests === totalTests && totalTests > 0) {
      console.log(`   ✅ 请求过滤功能正常工作！`);
      console.log(`   ✅ 第三方分析服务的警告已被过滤`);
      console.log(`   ✅ SHOPLINE变量检测功能不受影响`);
    } else {
      console.log(`   ⚠️  部分测试失败，需要检查配置`);
    }
    
    console.log(`\n💡 优化建议:`);
    console.log(`   - 监控服务器日志的清洁度`);
    console.log(`   - 根据需要调整过滤规则`);
    console.log(`   - 考虑添加性能优化选项`);
  }

  /**
   * 运行完整测试
   */
  async runCompleteTest() {
    console.log('🧪 === 请求过滤功能测试 ===\n');
    console.log('📋 本测试将验证第三方分析服务的请求失败警告是否被正确过滤');
    console.log('🔍 请同时观察服务器日志的输出变化\n');
    
    try {
      // 检查服务器状态
      await this.waitForServer();
      
      // 测试每个网站
      for (const site of TEST_SITES) {
        // 动态检测测试
        await this.testDynamicDetectionWithFiltering(site);
        
        // 等待一下避免请求过快
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 静态分析对比
        await this.testStaticAnalysisComparison(site);
        
        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 混合模式测试
        await this.testHybridModeFiltering(site);
        
        // 网站间等待
        if (site !== TEST_SITES[TEST_SITES.length - 1]) {
          console.log('\n⏳ Waiting before next site...');
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
      
      // 生成报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 测试失败:', error.message);
      process.exit(1);
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new RequestFilteringTest();
  test.runCompleteTest().catch(error => {
    console.error('测试错误:', error);
    process.exit(1);
  });
}

module.exports = RequestFilteringTest;
