/**
 * 真实 SHOPLINE 商店网站测试
 * 使用真实的 SHOPLINE 商店来验证变量检测功能
 */

const fetch = require('node-fetch');

const SERVER_URL = 'http://localhost:3001';

// 真实的 SHOPLINE 商店网站
const SHOPLINE_STORES = [
  {
    name: 'FaithDoodle Store',
    url: 'https://faithdoodle.shoplineapp.com/',
    expected: 'SHOPLINE 2.0'
  },
  {
    name: 'Bottle Demo Store', 
    url: 'https://bottle-demo.myshopline.com/',
    expected: 'SHOPLINE 1.0/2.0'
  }
];

// 对比网站（非SHOPLINE）
const NON_SHOPLINE_SITES = [
  {
    name: 'Shopify Store',
    url: 'https://www.shopify.com',
    expected: 'Non-SHOPLINE'
  },
  {
    name: 'Example Site',
    url: 'https://example.com',
    expected: 'Non-SHOPLINE'
  }
];

class RealShoplineTest {
  constructor() {
    this.results = [];
    this.performanceData = {};
  }

  /**
   * 等待服务器就绪
   */
  async waitForServer(maxAttempts = 5) {
    console.log('🔍 Checking if server is running...');
    
    for (let i = 0; i < maxAttempts; i++) {
      try {
        const response = await fetch(`${SERVER_URL}/health`, { timeout: 5000 });
        if (response.ok) {
          console.log('✅ Server is ready');
          return true;
        }
      } catch (error) {
        console.log(`⏳ Attempt ${i + 1}/${maxAttempts}: Server not ready, waiting...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    throw new Error('Server is not running. Please start the server first with: npm run dev');
  }

  /**
   * 测试单个网站的所有检测模式
   */
  async testWebsite(site) {
    console.log(`\n🏪 Testing: ${site.name}`);
    console.log(`📍 URL: ${site.url}`);
    console.log(`🎯 Expected: ${site.expected}`);
    
    const siteResults = {
      name: site.name,
      url: site.url,
      expected: site.expected,
      modes: {}
    };

    // 清除缓存确保新鲜测试
    await fetch(`${SERVER_URL}/cache/clear`, { method: 'POST' });

    // 1. 静态分析测试
    await this.testStaticAnalysis(site, siteResults);
    
    // 2. 增强检测 - 自动模式
    await this.testEnhancedDetection(site, siteResults, 'auto');
    
    // 3. 增强检测 - 静态模式
    await this.testEnhancedDetection(site, siteResults, 'static');
    
    // 4. 增强检测 - 动态模式
    await this.testEnhancedDetection(site, siteResults, 'dynamic');
    
    // 5. 增强检测 - 混合模式
    await this.testEnhancedDetection(site, siteResults, 'hybrid');

    this.results.push(siteResults);
    return siteResults;
  }

  /**
   * 测试静态分析
   */
  async testStaticAnalysis(site, siteResults) {
    console.log(`\n  🔬 Testing static analysis...`);
    
    try {
      const startTime = Date.now();
      const response = await fetch(`${SERVER_URL}/analyze/static`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          url: site.url,
          options: {
            enablePerformanceMetrics: true,
            enableDetailedReport: true
          }
        })
      });
      
      const responseTime = Date.now() - startTime;
      const data = await response.json();
      
      const result = {
        success: response.ok && data.success,
        responseTime,
        method: data.data?.method,
        foundVariables: data.data?.analysis?.foundCount || 0,
        variables: data.data?.results || [],
        confidence: data.data?.analysis?.confidence || 0,
        platformVersion: data.data?.analysis?.platformVersion,
        storeType: data.data?.analysis?.storeType,
        detectionTime: data.data?.timing?.total,
        hasShopline: data.data?.analysis?.hasShopline,
        performanceMetrics: data.data?.performanceMetrics
      };
      
      siteResults.modes.static = result;
      
      console.log(`     ✅ Success: ${result.success}`);
      console.log(`     ⏱️  Response time: ${responseTime}ms`);
      console.log(`     🔍 Detection time: ${result.detectionTime}ms`);
      console.log(`     📊 Found variables: ${result.foundVariables}`);
      console.log(`     🎯 Confidence: ${(result.confidence * 100).toFixed(1)}%`);
      console.log(`     🏪 Has SHOPLINE: ${result.hasShopline}`);
      console.log(`     📱 Platform: ${result.platformVersion || 'Unknown'}`);
      console.log(`     🏬 Store type: ${result.storeType || 'Unknown'}`);
      
      if (result.foundVariables > 0) {
        console.log(`     📋 Variables found:`);
        result.variables.forEach(v => {
          console.log(`       - ${v.name}: ${v.found ? '✅' : '❌'} (${v.size || 0} chars)`);
        });
      }
      
    } catch (error) {
      console.log(`     ❌ Error: ${error.message}`);
      siteResults.modes.static = { success: false, error: error.message };
    }
  }

  /**
   * 测试增强检测
   */
  async testEnhancedDetection(site, siteResults, mode) {
    console.log(`\n  🚀 Testing enhanced detection (${mode} mode)...`);
    
    try {
      const startTime = Date.now();
      const response = await fetch(`${SERVER_URL}/detect/enhanced`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          url: site.url,
          options: { 
            mode,
            enableComparison: mode === 'hybrid',
            timeout: 45000
          }
        })
      });
      
      const responseTime = Date.now() - startTime;
      const data = await response.json();
      
      const result = {
        success: response.ok && data.success,
        responseTime,
        requestedMode: mode,
        selectedMethod: data.data?.method,
        foundVariables: data.data?.analysis?.foundCount || 0,
        variables: data.data?.results || [],
        confidence: data.data?.analysis?.confidence || 0,
        platformVersion: data.data?.analysis?.platformVersion,
        storeType: data.data?.analysis?.storeType,
        detectionTime: data.data?.timing?.total,
        hasShopline: data.data?.analysis?.hasShopline,
        comparison: data.data?.comparison
      };
      
      siteResults.modes[mode] = result;
      
      console.log(`     ✅ Success: ${result.success}`);
      console.log(`     ⏱️  Response time: ${responseTime}ms`);
      console.log(`     🔄 Requested mode: ${mode}`);
      console.log(`     🎯 Selected method: ${result.selectedMethod}`);
      console.log(`     🔍 Detection time: ${result.detectionTime}ms`);
      console.log(`     📊 Found variables: ${result.foundVariables}`);
      console.log(`     🎯 Confidence: ${(result.confidence * 100).toFixed(1)}%`);
      console.log(`     🏪 Has SHOPLINE: ${result.hasShopline}`);
      console.log(`     📱 Platform: ${result.platformVersion || 'Unknown'}`);
      
      if (mode === 'hybrid' && result.comparison) {
        console.log(`     🔀 Hybrid comparison:`);
        console.log(`       - Variable agreement: ${(result.comparison.agreement?.variableAgreement * 100).toFixed(1)}%`);
        console.log(`       - Confidence agreement: ${(result.comparison.agreement?.confidenceAgreement * 100).toFixed(1)}%`);
        console.log(`       - Overall agreement: ${(result.comparison.agreement?.overallAgreement * 100).toFixed(1)}%`);
        console.log(`       - Recommended result: ${result.comparison.recommendedResult}`);
      }
      
      if (result.foundVariables > 0) {
        console.log(`     📋 Variables found:`);
        result.variables.forEach(v => {
          console.log(`       - ${v.name}: ${v.found ? '✅' : '❌'} (${v.size || 0} chars)`);
        });
      }
      
    } catch (error) {
      console.log(`     ❌ Error: ${error.message}`);
      siteResults.modes[mode] = { success: false, error: error.message };
    }
  }

  /**
   * 测试批量分析
   */
  async testBatchAnalysis() {
    console.log(`\n📦 Testing batch analysis with SHOPLINE stores...`);
    
    const urls = SHOPLINE_STORES.map(store => store.url);
    
    try {
      const startTime = Date.now();
      const response = await fetch(`${SERVER_URL}/analyze/static/batch`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          urls,
          options: {
            enablePerformanceMetrics: true
          }
        })
      });
      
      const responseTime = Date.now() - startTime;
      const data = await response.json();
      
      console.log(`   ✅ Success: ${response.ok && data.success}`);
      console.log(`   ⏱️  Total time: ${responseTime}ms`);
      console.log(`   📊 URLs processed: ${data.data?.results?.length || 0}`);
      console.log(`   📈 Success rate: ${data.data?.summary?.successful}/${data.data?.summary?.total}`);
      console.log(`   ⚡ Average time per URL: ${(responseTime / urls.length).toFixed(0)}ms`);
      
      if (data.data?.results) {
        data.data.results.forEach((result, index) => {
          const store = SHOPLINE_STORES[index];
          console.log(`   🏪 ${store.name}:`);
          console.log(`     - Variables: ${result.analysis?.foundCount || 0}`);
          console.log(`     - Confidence: ${((result.analysis?.confidence || 0) * 100).toFixed(1)}%`);
          console.log(`     - Has SHOPLINE: ${result.analysis?.hasShopline}`);
        });
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }

  /**
   * 性能对比分析
   */
  analyzePerformance() {
    console.log(`\n⚡ === 性能对比分析 ===`);
    
    const shoplineResults = this.results.filter(r => 
      r.url.includes('shoplineapp.com') || r.url.includes('myshopline.com')
    );
    
    if (shoplineResults.length === 0) {
      console.log('❌ 没有SHOPLINE网站的测试结果');
      return;
    }
    
    // 分析不同模式的性能
    const modes = ['static', 'auto', 'dynamic', 'hybrid'];
    
    console.log(`\n📊 各检测模式性能对比:`);
    console.log(`${'模式'.padEnd(10)} | ${'平均时间'.padEnd(10)} | ${'检测时间'.padEnd(10)} | ${'变量数'.padEnd(8)} | ${'置信度'.padEnd(8)}`);
    console.log(`${'-'.repeat(60)}`);
    
    modes.forEach(mode => {
      const modeResults = shoplineResults
        .map(r => r.modes[mode])
        .filter(m => m && m.success);
      
      if (modeResults.length > 0) {
        const avgResponseTime = modeResults.reduce((sum, m) => sum + (m.responseTime || 0), 0) / modeResults.length;
        const avgDetectionTime = modeResults.reduce((sum, m) => sum + (m.detectionTime || 0), 0) / modeResults.length;
        const avgVariables = modeResults.reduce((sum, m) => sum + (m.foundVariables || 0), 0) / modeResults.length;
        const avgConfidence = modeResults.reduce((sum, m) => sum + (m.confidence || 0), 0) / modeResults.length;
        
        console.log(`${mode.padEnd(10)} | ${avgResponseTime.toFixed(0).padEnd(10)}ms | ${avgDetectionTime.toFixed(0).padEnd(10)}ms | ${avgVariables.toFixed(1).padEnd(8)} | ${(avgConfidence * 100).toFixed(1).padEnd(8)}%`);
      }
    });
  }

  /**
   * 变量检测分析
   */
  analyzeVariableDetection() {
    console.log(`\n🔍 === 变量检测分析 ===`);
    
    const shoplineResults = this.results.filter(r => 
      r.url.includes('shoplineapp.com') || r.url.includes('myshopline.com')
    );
    
    shoplineResults.forEach(result => {
      console.log(`\n🏪 ${result.name}:`);
      console.log(`   URL: ${result.url}`);
      
      // 分析各模式的检测结果
      Object.entries(result.modes).forEach(([mode, data]) => {
        if (data.success) {
          console.log(`   ${mode.toUpperCase()}:`);
          console.log(`     - 变量数量: ${data.foundVariables}`);
          console.log(`     - 置信度: ${(data.confidence * 100).toFixed(1)}%`);
          console.log(`     - SHOPLINE检测: ${data.hasShopline ? '✅' : '❌'}`);
          console.log(`     - 平台版本: ${data.platformVersion || 'Unknown'}`);
          console.log(`     - 商店类型: ${data.storeType || 'Unknown'}`);
          
          if (data.variables && data.variables.length > 0) {
            const foundVars = data.variables.filter(v => v.found);
            if (foundVars.length > 0) {
              console.log(`     - 找到的变量:`);
              foundVars.forEach(v => {
                console.log(`       * ${v.name}: ${v.size} 字符`);
              });
            }
          }
        } else {
          console.log(`   ${mode.toUpperCase()}: ❌ 失败 - ${data.error || 'Unknown error'}`);
        }
      });
    });
  }

  /**
   * 生成综合报告
   */
  generateReport() {
    console.log(`\n📊 === 真实 SHOPLINE 网站测试报告 ===`);
    
    const totalTests = this.results.length;
    const shoplineTests = this.results.filter(r => 
      r.url.includes('shoplineapp.com') || r.url.includes('myshopline.com')
    ).length;
    
    console.log(`\n📈 测试概况:`);
    console.log(`   总测试网站: ${totalTests}`);
    console.log(`   SHOPLINE网站: ${shoplineTests}`);
    console.log(`   非SHOPLINE网站: ${totalTests - shoplineTests}`);
    
    // 统计成功率
    let totalModeTests = 0;
    let successfulModeTests = 0;
    
    this.results.forEach(result => {
      Object.values(result.modes).forEach(mode => {
        totalModeTests++;
        if (mode.success) successfulModeTests++;
      });
    });
    
    console.log(`   总模式测试: ${totalModeTests}`);
    console.log(`   成功测试: ${successfulModeTests}`);
    console.log(`   成功率: ${((successfulModeTests / totalModeTests) * 100).toFixed(1)}%`);
    
    // 性能分析
    this.analyzePerformance();
    
    // 变量检测分析
    this.analyzeVariableDetection();
    
    console.log(`\n🎯 结论:`);
    if (successfulModeTests / totalModeTests >= 0.8) {
      console.log(`✅ 真实SHOPLINE网站测试通过！系统能够正确检测SHOPLINE变量。`);
    } else {
      console.log(`⚠️  部分测试失败，需要进一步调试和优化。`);
    }
  }

  /**
   * 运行完整测试
   */
  async runCompleteTest() {
    console.log('🏪 === 真实 SHOPLINE 商店网站测试 ===\n');
    
    try {
      // 检查服务器状态
      await this.waitForServer();
      
      // 测试所有SHOPLINE网站
      console.log('\n🔍 Testing SHOPLINE stores...');
      for (const store of SHOPLINE_STORES) {
        await this.testWebsite(store);
      }
      
      // 测试对比网站
      console.log('\n🔍 Testing non-SHOPLINE sites for comparison...');
      for (const site of NON_SHOPLINE_SITES) {
        await this.testWebsite(site);
      }
      
      // 测试批量分析
      await this.testBatchAnalysis();
      
      // 生成报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 测试失败:', error.message);
      process.exit(1);
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new RealShoplineTest();
  test.runCompleteTest().catch(error => {
    console.error('测试错误:', error);
    process.exit(1);
  });
}

module.exports = RealShoplineTest;
