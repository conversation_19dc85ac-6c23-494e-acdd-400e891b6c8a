<!DOCTYPE html>
<html lang="en_US">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <meta name="keywords" content="SHOPLINE, Checker" />
        <meta name="description" content="<%- description -%>" />
        <link rel="shortcut icon" type="image/png" href="/favicon.png" />
        <link href="https://fonts.googleapis.com/css?family=Lato:100,300|PT+Sans:400,700" rel="stylesheet" />
        <title>SHOPLINE Checker</title>

        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'PT Sans', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }

            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                overflow: hidden;
            }

            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 40px;
                text-align: center;
            }

            .header h1 {
                font-size: 2.5rem;
                font-weight: 300;
                margin-bottom: 10px;
            }

            .header h1 span {
                font-weight: 700;
                color: #ffd700;
            }

            .header .description {
                font-size: 1.1rem;
                opacity: 0.9;
                line-height: 1.6;
            }

            .content {
                padding: 40px;
            }

            .form-group {
                margin-bottom: 25px;
            }

            .form-group label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #333;
                font-size: 1rem;
            }

            .form-group input {
                width: 100%;
                padding: 15px;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                font-size: 1rem;
                transition: border-color 0.3s ease;
            }

            .form-group input:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }

            .btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 1rem;
                font-weight: 600;
                cursor: pointer;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
                width: 100%;
            }

            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            }

            .btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }

            .loading {
                display: none;
                text-align: center;
                margin: 20px 0;
                color: #667eea;
                font-weight: 600;
            }

            .loading.show {
                display: block;
            }

            .result {
                margin-top: 30px;
                padding: 20px;
                border-radius: 8px;
                display: none;
            }

            .result.success {
                background: #f0f9ff;
                border: 2px solid #0ea5e9;
                display: block;
            }

            .result.error {
                background: #fef2f2;
                border: 2px solid #ef4444;
                display: block;
            }

            .result h3 {
                margin-bottom: 15px;
                color: #333;
            }

            .result.success h3 {
                color: #0ea5e9;
            }

            .result.error h3 {
                color: #ef4444;
            }

            .json-viewer {
                background: #1e293b;
                color: #e2e8f0;
                padding: 20px;
                border-radius: 8px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 0.9rem;
                line-height: 1.5;
                overflow-x: auto;
                max-height: 400px;
                overflow-y: auto;
            }

            .json-key {
                color: #7dd3fc;
            }

            .json-string {
                color: #86efac;
            }

            .json-number {
                color: #fbbf24;
            }

            .json-boolean {
                color: #f472b6;
            }

            .json-null {
                color: #94a3b8;
            }

            .variable-section {
                margin: 20px 0;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                overflow: hidden;
            }

            .variable-header {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                padding: 15px 20px;
                border-bottom: 1px solid #e2e8f0;
            }

            .variable-header h4 {
                margin: 0;
                color: #374151;
                font-size: 1.1rem;
                font-weight: 600;
            }

            .variable-content {
                padding: 0;
            }

            .variable-content .json-viewer {
                margin: 0;
                border-radius: 0;
            }

            .example {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 20px;
            }

            .example h4 {
                color: #475569;
                margin-bottom: 10px;
                font-size: 0.9rem;
            }

            .example code {
                background: #e2e8f0;
                padding: 2px 6px;
                border-radius: 4px;
                font-family: monospace;
                color: #475569;
            }
        </style>

        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://shoplinechecker.com" />
        <meta property="og:title" content="SHOPLINE Checker" />
        <meta property="og:description" content="Use the SHOPLINE Checker can help you find performance, SEO and product optimization issues in your SHOPLINE Store." />
        <meta property="og:image" content="https://shoplinechecker.com/shopline-checker.jpg" />
        <meta property="og:site_name" content="SHOPLINE Checker" />
        <meta property="og:locale" content="en_US" />

        <script type="text/javascript">
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "ndb8cpd38r");
        </script>

    </head>

    <body>
        <div class="container">
            <div class="header">
                <h1>SHOPLINE <span>Checker</span></h1>
                <p class="description">检测 SHOPLINE 网站中的关键变量，获取网站配置信息</p>
            </div>

            <div class="content">
                <div class="example">
                    <h4>📝 使用示例</h4>
                    <p>输入 SHOPLINE 网站 URL，例如：<code>https://charm-demo.myshopline.com/</code></p>
                    <p>系统将自动检测以下变量：<code>window.Shopline</code> 和 <code>window.mainConfig</code></p>
                </div>

                <form id="checker-form">
                    <div class="form-group">
                        <label for="url-input">🌐 网站 URL</label>
                        <input
                            type="url"
                            id="url-input"
                            placeholder="请输入要检测的网站 URL，例如：https://example.myshopline.com/"
                            required
                        />
                    </div>

                    <button type="submit" class="btn" id="submit-btn">
                        🔍 检测 SHOPLINE 变量
                    </button>
                </form>

                <div class="loading" id="loading">
                    <p>🔄 正在检测中，请稍候...</p>
                </div>

                <div class="result" id="result">
                    <h3 id="result-title"></h3>
                    <div id="result-content"></div>
                </div>
            </div>
        </div>

        <script>
            // API 配置
            const API_BASE_URL = 'http://localhost:3000';

            // DOM 元素
            const form = document.getElementById('checker-form');
            const urlInput = document.getElementById('url-input');
            const submitBtn = document.getElementById('submit-btn');
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            const resultTitle = document.getElementById('result-title');
            const resultContent = document.getElementById('result-content');

            // JSON 语法高亮函数
            function syntaxHighlight(json) {
                if (typeof json !== 'string') {
                    json = JSON.stringify(json, null, 2);
                }

                json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');

                return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                    let cls = 'json-number';
                    if (/^"/.test(match)) {
                        if (/:$/.test(match)) {
                            cls = 'json-key';
                        } else {
                            cls = 'json-string';
                        }
                    } else if (/true|false/.test(match)) {
                        cls = 'json-boolean';
                    } else if (/null/.test(match)) {
                        cls = 'json-null';
                    }
                    return '<span class="' + cls + '">' + match + '</span>';
                });
            }

            // 显示结果
            function showResult(success, title, content) {
                result.className = success ? 'result success' : 'result error';
                resultTitle.textContent = title;
                resultContent.innerHTML = content;
                result.style.display = 'block';
            }

            // 隐藏结果
            function hideResult() {
                result.style.display = 'none';
            }

            // 显示加载状态
            function showLoading() {
                loading.classList.add('show');
                submitBtn.disabled = true;
                submitBtn.textContent = '🔄 检测中...';
                hideResult();
            }

            // 隐藏加载状态
            function hideLoading() {
                loading.classList.remove('show');
                submitBtn.disabled = false;
                submitBtn.textContent = '🔍 检测 SHOPLINE 变量';
            }

            // 调用后端 API
            async function checkShoplineVariables(url) {
                const requestData = {
                    url: url,
                    options: {
                        timeout: 30000,
                        waitUntil: "domcontentloaded"
                    }
                };

                try {
                    const response = await fetch(`${API_BASE_URL}/detect/shopline`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestData)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    return data;
                } catch (error) {
                    throw error;
                }
            }

            // 表单提交处理
            form.addEventListener('submit', async (e) => {
                e.preventDefault();

                const url = urlInput.value.trim();
                if (!url) {
                    showResult(false, '❌ 输入错误', '<p>请输入有效的 URL</p>');
                    return;
                }

                showLoading();

                try {
                    const response = await checkShoplineVariables(url);

                    if (response.success && response.data) {
                        const { results, analysis } = response.data;

                        if (analysis.isShoplineStore) {
                            // 构建结果 HTML
                            let resultHtml = `
                                <div style="margin-bottom: 20px; padding: 15px; background: #f0f9ff; border-radius: 8px; border-left: 4px solid #0ea5e9;">
                                    <h4 style="margin: 0 0 10px 0; color: #0ea5e9;">🎯 检测分析</h4>
                                    <p><strong>网站类型:</strong> ${analysis.storeType}</p>
                                    ${analysis.storeVersion ? `<p><strong>版本:</strong> ${analysis.storeVersion}</p>` : ''}
                                    <p><strong>置信度:</strong> ${analysis.confidence}%</p>
                                    <p><strong>找到变量:</strong> ${analysis.totalVariablesFound} 个</p>
                                    <p><strong>数据大小:</strong> ${analysis.totalDataSize.toLocaleString()} 字符</p>
                                </div>
                            `;

                            // 显示找到的变量
                            const foundVariables = results.filter(r => r.found);
                            if (foundVariables.length > 0) {
                                resultHtml += '<div style="margin-bottom: 15px;"><h4>📦 检测到的变量:</h4>';
                                foundVariables.forEach(variable => {
                                    resultHtml += `
                                        <div style="margin: 10px 0; padding: 10px; background: #f8fafc; border-radius: 6px;">
                                            <strong>${variable.name}</strong> (${variable.type})
                                            ${variable.size ? ` - ${variable.size} 字符` : ''}
                                        </div>
                                    `;
                                });
                                resultHtml += '</div>';

                                // 显示所有找到的变量的 JSON 数据
                                foundVariables.forEach((variable, index) => {
                                    if (variable.value && typeof variable.value === 'object') {
                                        resultHtml += `
                                            <div class="variable-section">
                                                <div class="variable-header">
                                                    <h4>📦 ${variable.name} 变量内容 ${variable.size ? `(${variable.size.toLocaleString()} 字符)` : ''}</h4>
                                                </div>
                                                <div class="variable-content">
                                                    <div class="json-viewer">
                                                        <pre>${syntaxHighlight(variable.value)}</pre>
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                    } else if (variable.value !== null && variable.value !== undefined) {
                                        // 显示非对象类型的变量值
                                        resultHtml += `
                                            <div class="variable-section">
                                                <div class="variable-header">
                                                    <h4>📦 ${variable.name} 变量内容</h4>
                                                </div>
                                                <div style="padding: 20px;">
                                                    <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #6366f1;">
                                                        <code style="color: #374151; font-family: monospace;">${String(variable.value)}</code>
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                    }
                                });
                            }

                            resultHtml += `
                                <p style="margin-top: 15px; color: #64748b; font-size: 0.9rem;">
                                    ✅ 检测时间: ${new Date(response.timestamp).toLocaleString('zh-CN')}
                                </p>
                            `;

                            showResult(true, '🎉 检测到 SHOPLINE 网站！', resultHtml);
                        } else {
                            showResult(false, '❌ 未检测到 SHOPLINE 变量',
                                '<p>在该网站中未检测到 SHOPLINE 相关变量。</p>' +
                                '<p style="margin-top: 10px; color: #64748b; font-size: 0.9rem;">这可能意味着该网站不是 SHOPLINE 网站，或者相关变量未被加载。</p>' +
                                `<p style="margin-top: 10px; color: #64748b; font-size: 0.9rem;">检测了 ${results.length} 个变量，置信度: ${analysis.confidence}%</p>`
                            );
                        }
                    } else {
                        showResult(false, '❌ 检测失败', `<p>API 返回了意外的响应格式</p>`);
                    }
                } catch (error) {
                    console.error('检测错误:', error);
                    showResult(false, '❌ 检测失败',
                        `<p>检测过程中发生错误：${error.message}</p>` +
                        '<p style="margin-top: 10px; color: #64748b; font-size: 0.9rem;">请检查网络连接和后端服务是否正常运行。</p>'
                    );
                } finally {
                    hideLoading();
                }
            });

            // 页面加载完成后的初始化
            document.addEventListener('DOMContentLoaded', () => {
                // 设置示例 URL
                urlInput.value = 'https://charm-demo.myshopline.com/';

                // 添加输入验证
                urlInput.addEventListener('input', () => {
                    const url = urlInput.value.trim();
                    if (url && !url.match(/^https?:\/\/.+/)) {
                        urlInput.setCustomValidity('请输入有效的 HTTP 或 HTTPS URL');
                    } else {
                        urlInput.setCustomValidity('');
                    }
                });
            });
        </script>
        <script type="application/ld+json">
            {
                "@context": "http://schema.org",
                "@type": "WebSite",
                "name": "SHOPLINE Checker",
                "url": "https://shoplinechecker.com",
                "description": "Use the SHOPLINE Checker can help you find performance, SEO and product optimization issues in your SHOPLINE Store.",
                "publisher": {
                    "@type": "Organization",
                    "name": "SHOPLINE",
                    "logo": {
                        "@type": "ImageObject",
                        "url": "https://shoplinechecker.com/shopline-checker.jpg"
                    }
                }
            }
        </script>
    </body>
</html>
