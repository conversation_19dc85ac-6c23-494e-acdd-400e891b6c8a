/* 佈局教程
https://web.dev/i18n/zh/one-line-layouts/
https://zhuanlan.zhihu.com/p/31952490
https://segmentfault.com/a/1190000041097548
https://juejin.cn/post/7076276035565649957
https://juejin.cn/post/7070431502294581285
https://zhuanlan.zhihu.com/p/26415902
https://zhuanlan.zhihu.com/p/28368479

https://bashooka.com/coding/10-awesome-css-layout-tools/
https://www.creatisimo.net/css-layout-tools/
https://codingmasterweb.com/index.php/2022/03/31/4-css-layout-tools/
https://stackdiary.com/css-layout-generators/
https://css-tricks.com/hottest-front-end-tools-in-2021/
*/
body {
    margin: 0;
    padding: 0 20px;
    font-size: 16px;
    font-family: <PERSON><PERSON>, <PERSON><PERSON>, "Droid Sans", <PERSON><PERSON>, Sans-serif;
    color: #333;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
}
h1 {
    font-size: 36px;
    font-family: <PERSON><PERSON>, <PERSON><PERSON>, "Droid Sans", Arial, Sans-serif;
    font-weight: 700;
    margin: 10px 0;
    color: #353781;
    span {
        color:#f45f61;
    }
    + p {
        font-size: 14px;
        margin: -10px 0 10px;
        color: #666;
    }
}
.info-item {
    font-weight: 700;
    strong {
        font-weight: 400;
        margin-right: 2px;
    }
    span{
        font-size: 13px;
        color: #666;
    }
}
.yes{
    color: #417505;
    font-weight: 100;
}
.no{
    color: #cc0a0a;
    font-weight: 100;
}   

#browser-feature {
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    flex-wrap: wrap;
    .info-item {
        flex:0 0 33.33%;
        // float: left;
        box-sizing: border-box;
        font-size: 14px;
        padding: 0 0 0 5px;
        @media (min-width: 768px) {
            flex: 0 0 20%;
            padding:0 0 5px 20px;
        }
    }
}