// 引入 CSS
import './style.scss'

import $ from 'jquery'
// import { func } from '@js/func'
// import '@js/zi.js'
import { UAParser } from 'ua-parser-js';

// 执行某些函数
const ua = new UAParser();
const { browser, engine, os, device } = ua.getResult();
console.log(browser.name);
console.log(browser.version);
console.log(engine.name);
console.log(engine.version);
console.log(os.name);
console.log(os.version);
console.log(device.model);
console.log(device.vendor);
console.log(ua.getUA())
async function getAllUserAgentInfo() {
    // 获取基础信息
    const basic = {
        brands: navigator.userAgentData.brands,
        mobile: navigator.userAgentData.mobile,
        platform: navigator.userAgentData.platform
    };

    try {
        // 获取所有高熵值信息
        const highEntropyValues = await navigator.userAgentData.getHighEntropyValues([
            'architecture',
            'bitness',
            'model',
            'platformVersion',
            'uaFullVersion',
            'fullVersionList',
            'wow64'
        ]);

        return {
            ...basic,
            ...highEntropyValues
        };
    } catch (error) {
        console.error('获取高熵值信息失败：', error);
        return basic;
    }
}

// 使用示例
getAllUserAgentInfo().then(info => {
    console.log('完整的 UserAgent 信息：', info);
});


// 浏览器基本信息收集函数
function getBasicBrowserInfo(device, os, browser, engine) {
    return [
        {
            label: 'Device',
            value: `${device.vendor || ''} ${device.model || ''} ${device.type || 'desktop'}`
        },
        {
            label: 'OS',
            value: `${os.name} ${os.version}`
        },
        {
            label: 'Browser',
            value: `${browser.name} ${browser.version}`
        },
        {
            label: 'Engine',
            value: `${engine.name} ${engine.version}`
        }
    ];
}

// 获取系统环境信息
function getSystemInfo() {
    return [
        {
            label: 'Language',
            value: navigator.language
        },
        {
            label: 'Screen Size',
            value: `${screen.width} x ${screen.height}`
        },
        {
            label: 'Window Size',
            value: `${window.innerWidth} x ${window.innerHeight}`,
            id: 'window-size'
        },
        {
            label: 'Color Depth',
            value: `${screen.colorDepth}bit`
        },
        {
            label: 'Pixel Ratio',
            value: window.devicePixelRatio
        }
    ];
}

// 获取时区信息
function getTimeZoneInfo() {
    const timeZoneName = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const offset = new Date().getTimezoneOffset() / 60;
    const sign = offset < 0 ? '+' : '-';
    const absOffset = Math.abs(offset).toString().padStart(2, '0');
    return {
        label: 'Time Zone',
        value: `${timeZoneName} (${sign}${absOffset}:00)`
    };
}

// 浏览器特性检测配置
const featureDetectionConfig = {
    Cookie: () => navigator.cookieEnabled,
    Storage: () => typeof Storage !== "undefined",
    IndexedDB: () => !!window.indexedDB,
    WebSQL: () => !!window.openDatabase,
    'Web Workers': () => !!window.Worker,
    Canvas: () => !!window.CanvasRenderingContext2D,
    WebGL: () => !!window.WebGLRenderingContext,
    WebRTC: () => !!navigator.getUserMedia,
    WebSocket: () => !!window.WebSocket,
    'Service Worker': () => !!navigator.serviceWorker,
    WebAssembly: () => !!window.WebAssembly,
    WebP: () => document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0,
    EXIF: () => checkExifSupport(),
    'Do Not Track': () => !!navigator.doNotTrack,
    'Ads Blocker': () => !!window.navigator.userAgentData
};

// 检查EXIF支持
function checkExifSupport() {
    return typeof window.FileReader !== 'undefined' &&
           typeof window.FileReader.prototype?.readAsArrayBuffer !== 'undefined' &&
           typeof window.DataView?.prototype?.getUint8 !== 'undefined';
}

// 生成HTML元素
function createInfoItem(label, value, id = '') {
    const idAttr = id ? ` id="${id}"` : '';
    return `<div class="info-item"${idAttr}><strong>${label}: </strong>${value}</div>`;
}

// 生成特性检测HTML
function createFeatureItem(label, supported) {
    const icon = supported ? '<b class="yes">✔</b>' : '<b class="no">✖</b>';
    return createInfoItem(label, icon);
}

// 主函数
function updateBrowserInfo() {
    try {
        let browserInfo = '';
        let browserFeature = '';

        // 基本信息
        getBasicBrowserInfo(device, os, browser, engine)
            .forEach(item => browserInfo += createInfoItem(item.label, item.value, item.id));

        // 系统信息
        getSystemInfo()
            .forEach(item => browserInfo += createInfoItem(item.label, item.value, item.id));

        // 时区信息
        const timeZoneInfo = getTimeZoneInfo();
        browserInfo += createInfoItem(timeZoneInfo.label, timeZoneInfo.value);

        // UserAgent信息
        browserInfo += createInfoItem('User Agent', `<span>${navigator.userAgent}</span>`);
        
        // IP地址占位
        browserInfo += createInfoItem('IP address', '<span id="ip-address">Checking...</span>');

        // 特性检测
        Object.entries(featureDetectionConfig).forEach(([feature, detector]) => {
            browserFeature += createFeatureItem(feature, detector());
        });

        // 更新DOM
        $('#browser-info').html(browserInfo);
        $('#browser-feature').html(browserFeature);
    } catch (error) {
        console.error("无法获取浏览器信息：", error);
    }
}
updateBrowserInfo();
/**
 * 检测IP地址
 * @returns {Promise<void>}
 */
async function detectIP() {
    const ipElement = document.getElementById('ip-address');
    if (!ipElement) return;

    try {
        const response = await fetch('https://api.ipify.org?format=json', {
            timeout: 5000
        });

        if (!response.ok) {
            throw new Error('网络请求失败');
        }

        const { ip } = await response.json();
        if (!ip) {
            throw new Error('无效的IP数据');
        }

        ipElement.textContent = ip;

    } catch (error) {
        console.error('IP检测失败:', error);
        ipElement.textContent = '检测失败，请稍后重试';
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', detectIP);
/**
 * 更新窗口尺寸信息
 */
const updateWindowInnerSize = () => {
    const sizeElement = document.getElementById('window-size');
    if (!sizeElement) {
        console.warn('未找到窗口尺寸显示元素 (id="window-size")');
        return;
    }
    
    sizeElement.innerHTML = createInfoItem(
        'Window Size', 
        `${window.innerWidth} x ${window.innerHeight}`
    );
};

// 初始化并添加事件监听
updateWindowInnerSize();
window.addEventListener('resize', updateWindowInnerSize);