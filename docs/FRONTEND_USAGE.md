# ShopLine Checker 前端使用指南

## 🎯 功能概述

ShopLine Checker 前端页面提供了一个简洁的界面，用于检测 SHOPLINE 网站中的 `window.__ENV__` 变量。

## 🚀 快速开始

### 1. 启动服务

确保后端和前端服务都在运行：

```bash
# 启动后端服务 (在 server 目录)
cd server
npm run pm2:start

# 启动前端服务 (在根目录)
cd ..
npm run dev
```

### 2. 访问页面

打开浏览器访问：`http://localhost:5174/`

## 🔍 使用步骤

### 步骤 1: 输入 URL
在输入框中输入要检测的 SHOPLINE 网站 URL，例如：
- `https://charm-demo.myshopline.com/`
- `https://your-store.myshopline.com/`

### 步骤 2: 点击检测
点击 "🔍 检测 __ENV__ 变量" 按钮开始检测。

### 步骤 3: 查看结果
- **成功**: 显示绿色结果框，包含完整的 JSON 数据
- **失败**: 显示红色错误框，说明失败原因

## 📊 结果说明

### 成功检测
当成功检测到 `window.__ENV__` 变量时，页面会显示：
- ✅ 成功标题
- 📦 完整的 JSON 数据（语法高亮）
- ⏰ 检测时间戳

### 检测失败
可能的失败原因：
- 网站不是 SHOPLINE 网站
- 网站未加载 `__ENV__` 变量
- 网络连接问题
- 网站访问受限

## 🎨 界面特性

### 设计特点
- **响应式设计**: 适配各种屏幕尺寸
- **现代 UI**: 渐变背景和卡片式布局
- **语法高亮**: JSON 数据彩色显示
- **加载状态**: 实时显示检测进度

### 交互功能
- **输入验证**: 自动验证 URL 格式
- **实时反馈**: 显示加载状态和结果
- **错误处理**: 友好的错误提示

## 🔧 技术实现

### 前端技术栈
- **HTML5**: 语义化标记
- **CSS3**: 现代样式和动画
- **Vanilla JavaScript**: 原生 JS 实现
- **Fetch API**: 异步 HTTP 请求

### API 集成
- **端点**: `POST http://localhost:3000/detect`
- **CORS**: 已配置跨域访问
- **超时**: 30 秒检测超时
- **错误处理**: 完整的错误捕获

## 📱 示例用法

### 检测 SHOPLINE 演示站点
```
URL: https://charm-demo.myshopline.com/
结果: 成功检测到包含配置信息的 __ENV__ 对象
```

### 检测非 SHOPLINE 网站
```
URL: https://example.com/
结果: 未找到 __ENV__ 变量
```

## 🛠️ 开发调试

### 浏览器开发者工具
1. 打开 F12 开发者工具
2. 查看 Console 标签页的日志
3. 查看 Network 标签页的 API 请求

### 常见问题
1. **CORS 错误**: 确保后端服务正在运行
2. **超时错误**: 检查网络连接和目标网站状态
3. **解析错误**: 检查 API 响应格式

## 📈 性能优化

### 检测速度
- 平均检测时间: 3-5 秒
- 超时设置: 30 秒
- 并发限制: 单个请求

### 用户体验
- 加载动画显示检测进度
- 结果缓存避免重复检测
- 错误提示帮助用户排查问题

## 🔒 安全考虑

### 数据安全
- 不存储用户输入的 URL
- 不缓存检测结果
- 仅检测公开可访问的网站

### 隐私保护
- 不收集用户个人信息
- 不跟踪用户行为
- 本地化处理所有数据
